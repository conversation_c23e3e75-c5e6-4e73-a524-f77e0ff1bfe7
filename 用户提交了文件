用户提交了文件
@echo off
title 571zx
mode con cols=82 lines=80
taskkill /F /IM WmiPrvSE.exe
:on
cls
echo [93m
for /f "tokens=* delims=" %%a in ('reg query "HKLM\HARDWARE\DESCRIPTION\System\BIOS" /v "SystemManufacturer"') do (set "Brand=%%a")
echo.                 品牌：%Brand:~36%
for /f "tokens=* delims=" %%a in ('reg query "HKLM\HARDWARE\DESCRIPTION\System\BIOS" /v "BaseBoardProduct"') do (set "model=%%a")
echo.                 型号：%model:~34%
echo.
echo [93m=================================================================================[93m
echo.主板名称
wmic csproduct get Name
echo [93m=================================================================================[93m
echo.硬盘物理序列号　　　　　　　　
wmic diskdrive get serialnumber
echo [93m=================================================================================[93m
echo.内存条时序
wmic memorychip get partnumber
echo [93m=================================================================================[93m
echo.内存条序列号
wmic memorychip get SerialNumber
echo [93m=================================================================================[93m
echo.主板CPU序列号　　　　　　　　 
wmic cpu get serialnumber
echo [93m=================================================================================[93m
echo.主板biso序列号　　　　　　　　
wmic bios get serialnumber
echo [93m=================================================================================[93m
echo.主板物理序列号　　　　　　　　
wmic baseboard get serialnumber
echo [93m=================================================================================[93m　　　
echo.主板UUID
wmic csproduct get uuid
echo [92m=================================================================================[92m
echo.显卡虚拟序列号
nvidia-smi -L
echo [96m=================================================================================[96m
@echo off&&setlocal EnableDelayedExpansion
for /f "tokens=*" %%i in ('ipconfig /all^|findstr /i "描述 物理地址"') do set "qq=%%i"&&set "qq=!qq:. =!"&&echo.!qq!
getmac
echo [37m=================================================================================[97m
ipconfig | findstr /C:"默认网关" | findstr /R "[0-9]"
echo [95m
curl https://myip.ipip.net
echo.
pause>nul
taskkill /f /im WmiPrvSE.exe
goto on

并且用户提交了自己对可疑USB设备的反应，经过我们线下真实查询发现确实是USB拓展坞，ID号与查询号一直但是未在线下查询到有外挂设备接入。
接着用户说明自己蓝屏的原因，是因为这个动态机器码是通过其他商家购买的程序打开使用的。
用户说他的刷机7月13号零点29分开始刷动态机器码，然后刷新完成以后出现了查询到的蓝屏代码和异常行为。
用户说这个动态机器码刷完以后程序就自己删除了，并且桌面留下了一个可以开机以后自己查询硬件信息的程序。可以查询自己的机器码是否更改。
目前用户就提交了这么多证据，请你重新审视情况作出判断。