用户提交了文件
@echo off
title 571zx
mode con cols=82 lines=80
taskkill /F /IM WmiPrvSE.exe
:on
cls
echo [93m
for /f "tokens=* delims=" %%a in ('reg query "HKLM\HARDWARE\DESCRIPTION\System\BIOS" /v "SystemManufacturer"') do (set "Brand=%%a")
echo.                 品牌：%Brand:~36%
for /f "tokens=* delims=" %%a in ('reg query "HKLM\HARDWARE\DESCRIPTION\System\BIOS" /v "BaseBoardProduct"') do (set "model=%%a")
echo.                 型号：%model:~34%
echo.
echo [93m=================================================================================[93m
echo.主板名称
wmic csproduct get Name
echo [93m=================================================================================[93m
echo.硬盘物理序列号　　　　　　　　
wmic diskdrive get serialnumber
echo [93m=================================================================================[93m
echo.内存条时序
wmic memorychip get partnumber
echo [93m=================================================================================[93m
echo.内存条序列号
wmic memorychip get SerialNumber
echo [93m=================================================================================[93m
echo.主板CPU序列号　　　　　　　　 
wmic cpu get serialnumber
echo [93m=================================================================================[93m
echo.主板biso序列号　　　　　　　　
wmic bios get serialnumber
echo [93m=================================================================================[93m
echo.主板物理序列号　　　　　　　　
wmic baseboard get serialnumber
echo [93m=================================================================================[93m　　　
echo.主板UUID
wmic csproduct get uuid
echo [92m=================================================================================[92m
echo.显卡虚拟序列号
nvidia-smi -L
echo [96m=================================================================================[96m
@echo off&&setlocal EnableDelayedExpansion
for /f "tokens=*" %%i in ('ipconfig /all^|findstr /i "描述 物理地址"') do set "qq=%%i"&&set "qq=!qq:. =!"&&echo.!qq!
getmac
echo [37m=================================================================================[97m
ipconfig | findstr /C:"默认网关" | findstr /R "[0-9]"
echo [95m
curl https://myip.ipip.net
echo.
pause>nul
taskkill /f /im WmiPrvSE.exe
goto on
