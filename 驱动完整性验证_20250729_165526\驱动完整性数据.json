[{"filename": "win32k.sys", "base_address": "FFFFE026`10310000", "end_address": "FFFFE026`103BC000", "size": "0x000ac000", "load_count": "2", "load_order_group": "156", "driver_type": "System Driver", "description": "Multi-User Win32 Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:37", "file_modified_time": "2025/4/25 22:24:37", "full_path": "C:\\Windows\\System32\\win32k.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "cdd.dll", "base_address": "FFFFE026`10400000", "end_address": "FFFFE026`10447000", "size": "0x00047000", "load_count": "1", "load_order_group": "165", "driver_type": "Display Driver", "description": "Canonical Display Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:31", "file_modified_time": "2025/4/25 22:24:31", "full_path": "C:\\Windows\\System32\\cdd.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "win32kfull.sys", "base_address": "FFFFE026`10A00000", "end_address": "FFFFE026`10DD7000", "size": "0x003d7000", "load_count": "2", "load_order_group": "162", "driver_type": "System Driver", "description": "Full/Desktop Win32k Kernel Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:37", "file_modified_time": "2025/4/25 22:24:37", "full_path": "C:\\Windows\\System32\\win32kfull.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "mcupdate_GenuineIntel.dll", "base_address": "FFFFF800`6FCB0000", "end_address": "FFFFF800`70033000", "size": "0x00383000", "load_count": "1", "load_order_group": "3", "driver_type": "Communications Driver", "description": "Intel Microcode Update Library", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\system32\\mcupdate_GenuineIntel.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "hal.dll", "base_address": "FFFFF800`70040000", "end_address": "FFFFF800`70046000", "size": "0x00006000", "load_count": "1", "load_order_group": "1", "driver_type": "Dynamic Link Library", "description": "Hardware Abstraction Layer DLL", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\system32\\hal.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "kd.dll", "base_address": "FFFFF800`70050000", "end_address": "FFFFF800`7005B000", "size": "0x0000b000", "load_count": "2", "load_order_group": "2", "driver_type": "Communications Driver", "description": "Local Kernel Debugger", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\system32\\kd.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "tm.sys", "base_address": "FFFFF800`70060000", "end_address": "FFFFF800`7008A000", "size": "0x0002a000", "load_count": "1", "load_order_group": "5", "driver_type": "System Driver", "description": "Kernel Transaction Manager Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\drivers\\tm.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "CLFS.SYS", "base_address": "FFFFF800`70090000", "end_address": "FFFFF800`700FF000", "size": "0x0006f000", "load_count": "4", "load_order_group": "4", "driver_type": "System Driver", "description": "Common Log File System Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\drivers\\CLFS.SYS", "file_attributes": "A", "digital_signature": "CLFS", "certificate_issuer": "Common Log (CLFS)", "certificate_subject": ""}, {"filename": "PSHED.dll", "base_address": "FFFFF800`70100000", "end_address": "FFFFF800`7011B000", "size": "0x0001b000", "load_count": "2", "load_order_group": "6", "driver_type": "System Driver", "description": "特定于平台的硬件错误驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\system32\\PSHED.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "BOOTVID.dll", "base_address": "FFFFF800`70120000", "end_address": "FFFFF800`7012D000", "size": "0x0000d000", "load_count": "1", "load_order_group": "7", "driver_type": "Display Driver", "description": "VGA Boot Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\system32\\BOOTVID.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "FLTMGR.SYS", "base_address": "FFFFF800`70130000", "end_address": "FFFFF800`701AA000", "size": "0x0007a000", "load_count": "15", "load_order_group": "8", "driver_type": "System Driver", "description": "Microsoft 文件系统筛选器管理器", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\drivers\\FLTMGR.SYS", "file_attributes": "A", "digital_signature": "FltMgr", "certificate_issuer": "FltMgr", "certificate_subject": ""}, {"filename": "ksecdd.sys", "base_address": "FFFFF800`701B0000", "end_address": "FFFFF800`701DE000", "size": "0x0002e000", "load_count": "31", "load_order_group": "10", "driver_type": "System Driver", "description": "Kernel Security Support Provider Interface", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\System32\\drivers\\ksecdd.sys", "file_attributes": "A", "digital_signature": "KSecDD", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "cmimcext.sys", "base_address": "FFFFF800`701E0000", "end_address": "FFFFF800`701F2000", "size": "0x00012000", "load_count": "1", "load_order_group": "12", "driver_type": "System Driver", "description": "内核配置管理器初始配置扩展主机导出驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\drivers\\cmimcext.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "peauth.sys", "base_address": "FFFFF800`70800000", "end_address": "FFFFF800`708D1000", "size": "0x000d1000", "load_count": "1", "load_order_group": "188", "driver_type": "System Driver", "description": "Protected Environment Authentication and Authorization Export Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:24", "file_modified_time": "2025/4/25 22:24:24", "full_path": "C:\\Windows\\system32\\drivers\\peauth.sys", "file_attributes": "A", "digital_signature": "PEAUTH", "certificate_issuer": "PEAUTH", "certificate_subject": ""}, {"filename": "tcpipreg.sys", "base_address": "FFFFF800`708E0000", "end_address": "FFFFF800`708F5000", "size": "0x00015000", "load_count": "1", "load_order_group": "189", "driver_type": "Application", "description": "TCP/IP Registry Compatibility Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:45", "file_modified_time": "2025/4/25 22:24:45", "full_path": "C:\\Windows\\System32\\drivers\\tcpipreg.sys", "file_attributes": "A", "digital_signature": "tcpipreg", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "srv2.sys", "base_address": "FFFFF800`70900000", "end_address": "FFFFF800`709D7000", "size": "0x000d7000", "load_count": "1", "load_order_group": "190", "driver_type": "Network Driver", "description": "Smb 2.0 服务器驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\DRIVERS\\srv2.sys", "file_attributes": "A", "digital_signature": "srv2", "certificate_issuer": "Server SMB 2.xxx Driver", "certificate_subject": ""}, {"filename": "vwifimp.sys", "base_address": "FFFFF800`709E0000", "end_address": "FFFFF800`709F5000", "size": "0x00015000", "load_count": "1", "load_order_group": "191", "driver_type": "Network Driver", "description": "Virtual WiFi Miniport Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:11", "file_modified_time": "2022/5/7 13:19:11", "full_path": "C:\\Windows\\System32\\drivers\\vwifimp.sys", "file_attributes": "A", "digital_signature": "vwifimp", "certificate_issuer": "Virtual WiFi Miniport Service", "certificate_subject": ""}, {"filename": "ACE-BASE.sys", "base_address": "FFFFF800`70A00000", "end_address": "FFFFF800`710A6000", "size": "0x006a6000", "load_count": "1", "load_order_group": "203", "driver_type": "Installable Driver", "description": "ACE-BASE64 System Driver", "version": "21.0.2506.16640", "company": "ANTICHEATEXPERT.COM", "product_name": "Anti-Cheat Expert", "file_created_time": "2025/7/29 12:47:28", "file_modified_time": "2025/7/29 12:47:28", "full_path": "C:\\Windows\\system32\\drivers\\ACE-BASE.sys", "file_attributes": "A", "digital_signature": "ACE-BASE", "certificate_issuer": "ACE-BASE", "certificate_subject": ""}, {"filename": "mrxsmb20.sys", "base_address": "FFFFF800`714C0000", "end_address": "FFFFF800`7150F000", "size": "0x0004f000", "load_count": "1", "load_order_group": "184", "driver_type": "System Driver", "description": "Longhorn SMB 2.0 Redirector", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\system32\\DRIVERS\\mrxsmb20.sys", "file_attributes": "A", "digital_signature": "mrxsmb20", "certificate_issuer": "SMB 2.0 MiniRedirector", "certificate_subject": ""}, {"filename": "condrv.sys", "base_address": "FFFFF800`71510000", "end_address": "FFFFF800`71523000", "size": "0x00013000", "load_count": "1", "load_order_group": "185", "driver_type": "System Driver", "description": "<PERSON><PERSON><PERSON>", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:23", "file_modified_time": "2022/5/7 13:19:23", "full_path": "C:\\Windows\\System32\\drivers\\condrv.sys", "file_attributes": "A", "digital_signature": "condrv", "certificate_issuer": "<PERSON><PERSON><PERSON>", "certificate_subject": ""}, {"filename": "srvnet.sys", "base_address": "FFFFF800`71530000", "end_address": "FFFFF800`7158E000", "size": "0x0005e000", "load_count": "2", "load_order_group": "186", "driver_type": "Network Driver", "description": "Server Network driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\DRIVERS\\srvnet.sys", "file_attributes": "A", "digital_signature": "srvnet", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Ndu.sys", "base_address": "FFFFF800`71590000", "end_address": "FFFFF800`715BE000", "size": "0x0002e000", "load_count": "1", "load_order_group": "187", "driver_type": "Network Driver", "description": "Windows Network Data Usage Monitoring Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:10", "file_modified_time": "2025/4/25 22:25:10", "full_path": "C:\\Windows\\system32\\drivers\\Ndu.sys", "file_attributes": "A", "digital_signature": "Ndu", "certificate_issuer": "Windows Network Data Usage Monitoring Driver", "certificate_subject": ""}, {"filename": "ntoskrnl.exe", "base_address": "FFFFF800`72C00000", "end_address": "FFFFF800`73C47000", "size": "0x01047000", "load_count": "1", "load_order_group": "0", "driver_type": "Application", "description": "NT Kernel & System", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\system32\\ntoskrnl.exe", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "clipsp.sys", "base_address": "FFFFF800`75600000", "end_address": "FFFFF800`75716000", "size": "0x00116000", "load_count": "3", "load_order_group": "11", "driver_type": "System Driver", "description": "CLIP Service", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:31", "file_modified_time": "2025/4/25 22:24:31", "full_path": "C:\\Windows\\System32\\drivers\\clipsp.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "msrpc.sys", "base_address": "FFFFF800`75720000", "end_address": "FFFFF800`75782000", "size": "0x00062000", "load_count": "18", "load_order_group": "9", "driver_type": "System Driver", "description": "Kernel Remote Procedure Call Provider", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\System32\\drivers\\msrpc.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "werkernel.sys", "base_address": "FFFFF800`75790000", "end_address": "FFFFF800`757A6000", "size": "0x00016000", "load_count": "5", "load_order_group": "13", "driver_type": "System Driver", "description": "Windows Error Reporting Kernel Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:45", "file_modified_time": "2025/4/25 22:24:45", "full_path": "C:\\Windows\\System32\\drivers\\werkernel.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "ntosext.sys", "base_address": "FFFFF800`757B0000", "end_address": "FFFFF800`757BC000", "size": "0x0000c000", "load_count": "1", "load_order_group": "14", "driver_type": "System Driver", "description": "NTOS extension host driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:39", "file_modified_time": "2022/5/7 13:19:39", "full_path": "C:\\Windows\\System32\\drivers\\ntosext.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "CI.dll", "base_address": "FFFFF800`757C0000", "end_address": "FFFFF800`758BB000", "size": "0x000fb000", "load_count": "3", "load_order_group": "15", "driver_type": "System Driver", "description": "Code Integrity Module", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\system32\\CI.dll", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "cng.sys", "base_address": "FFFFF800`758C0000", "end_address": "FFFFF800`7597E000", "size": "0x000be000", "load_count": "18", "load_order_group": "16", "driver_type": "System Driver", "description": "Kernel Cryptography, Next Generation", "version": "10.0.22621.5192", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\drivers\\cng.sys", "file_attributes": "A", "digital_signature": "CNG", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Wdf01000.sys", "base_address": "FFFFF800`75980000", "end_address": "FFFFF800`75A47000", "size": "0x000c7000", "load_count": "1", "load_order_group": "17", "driver_type": "System Driver", "description": "内核模式驱动程序框架运行时", "version": "1.33.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\system32\\drivers\\Wdf01000.sys", "file_attributes": "A", "digital_signature": "Wdf01000", "certificate_issuer": "Kernel Mode Driver Frameworks service", "certificate_subject": ""}, {"filename": "WDFLDR.SYS", "base_address": "FFFFF800`75A50000", "end_address": "FFFFF800`75A67000", "size": "0x00017000", "load_count": "52", "load_order_group": "19", "driver_type": "System Driver", "description": "Kernel Mode Driver Framework Loader", "version": "1.33.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\system32\\drivers\\WDFLDR.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "WppRecorder.sys", "base_address": "FFFFF800`75A70000", "end_address": "FFFFF800`75A83000", "size": "0x00013000", "load_count": "46", "load_order_group": "18", "driver_type": "System Driver", "description": "WPP Trace Recorder", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\system32\\drivers\\WppRecorder.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "PRM.sys", "base_address": "FFFFF800`75A90000", "end_address": "FFFFF800`75A9E000", "size": "0x0000e000", "load_count": "1", "load_order_group": "20", "driver_type": "System Driver", "description": "Windows PRM Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:03", "file_modified_time": "2022/5/7 13:19:03", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\prm.inf_amd64_de435dc5c75d64a5\\PRM.sys", "file_attributes": "A", "digital_signature": "PRM", "certificate_issuer": "@prm.inf,%PRM.SvcDesc%;Microsoft PRM Driver", "certificate_subject": ""}, {"filename": "acpiex.sys", "base_address": "FFFFF800`75AA0000", "end_address": "FFFFF800`75AC7000", "size": "0x00027000", "load_count": "1", "load_order_group": "21", "driver_type": "Dynamic Link Library", "description": "ACPIEx Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\Drivers\\acpiex.sys", "file_attributes": "A", "digital_signature": "acpiex", "certificate_issuer": "Microsoft ACPIEx Driver", "certificate_subject": ""}, {"filename": "ACPI.sys", "base_address": "FFFFF800`75AD0000", "end_address": "FFFFF800`75B88000", "size": "0x000b8000", "load_count": "1", "load_order_group": "22", "driver_type": "System Driver", "description": "用于 NT 的 ACPI 驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\ACPI.sys", "file_attributes": "A", "digital_signature": "ACPI", "certificate_issuer": "@acpi.inf,%ACPI.SvcDesc%;Microsoft ACPI Driver", "certificate_subject": ""}, {"filename": "WMILIB.SYS", "base_address": "FFFFF800`75B90000", "end_address": "FFFFF800`75B9C000", "size": "0x0000c000", "load_count": "19", "load_order_group": "23", "driver_type": "System Driver", "description": "WMILIB WMI support library Dll", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\System32\\drivers\\WMILIB.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "msisadrv.sys", "base_address": "FFFFF800`75BA0000", "end_address": "FFFFF800`75BAB000", "size": "0x0000b000", "load_count": "1", "load_order_group": "24", "driver_type": "System Driver", "description": "ISA Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:04", "file_modified_time": "2022/5/7 13:19:04", "full_path": "C:\\Windows\\System32\\drivers\\msisadrv.sys", "file_attributes": "A", "digital_signature": "msisadrv", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "pci.sys", "base_address": "FFFFF800`75BB0000", "end_address": "FFFFF800`75C3C000", "size": "0x0008c000", "load_count": "1", "load_order_group": "25", "driver_type": "Dynamic Link Library", "description": "NT 即插即用 PCI 仿真程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\drivers\\pci.sys", "file_attributes": "A", "digital_signature": "pci", "certificate_issuer": "@pci.inf,%pci_svcdesc%;PCI Bus Driver", "certificate_subject": ""}, {"filename": "tpm.sys", "base_address": "FFFFF800`75C40000", "end_address": "FFFFF800`75C97000", "size": "0x00057000", "load_count": "1", "load_order_group": "26", "driver_type": "System Driver", "description": "TPM 设备驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\tpm.sys", "file_attributes": "A", "digital_signature": "TPM", "certificate_issuer": "@tpm.inf,%TPM%;TPM", "certificate_subject": ""}, {"filename": "intelpep.sys", "base_address": "FFFFF800`75CA0000", "end_address": "FFFFF800`75D26000", "size": "0x00086000", "load_count": "1", "load_order_group": "27", "driver_type": "Communications Driver", "description": "Intel Power Engine Plugin", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\intelpep.sys", "file_attributes": "A", "digital_signature": "intelpep", "certificate_issuer": "@intelpep.inf,%INTELPEP.SVCDESC%;Intel(R) Power Engine Plug-in Driver", "certificate_subject": ""}, {"filename": "WindowsTrustedRT.sys", "base_address": "FFFFF800`75D30000", "end_address": "FFFFF800`75D48000", "size": "0x00018000", "load_count": "1", "load_order_group": "28", "driver_type": "System Driver", "description": "Windows Trusted Runtime Interface Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\system32\\drivers\\WindowsTrustedRT.sys", "file_attributes": "A", "digital_signature": "WindowsTrustedRT", "certificate_issuer": "Windows Trusted Execution Environment Class Extension", "certificate_subject": ""}, {"filename": "IntelPMT.sys", "base_address": "FFFFF800`75D50000", "end_address": "FFFFF800`75D63000", "size": "0x00013000", "load_count": "1", "load_order_group": "29", "driver_type": "Communications Driver", "description": "Intel Platform Monitoring Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\IntelPMT.sys", "file_attributes": "A", "digital_signature": "IntelPMT", "certificate_issuer": "@intelpmt.inf,%IntelPMT.SVCDESC%;Intel(R) Platform Monitoring Technology Service", "certificate_subject": ""}, {"filename": "WindowsTrustedRTProxy.sys", "base_address": "FFFFF800`75D70000", "end_address": "FFFFF800`75D7B000", "size": "0x0000b000", "load_count": "1", "load_order_group": "30", "driver_type": "System Driver", "description": "Windows Trusted Runtime Service Proxy Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:25", "file_modified_time": "2022/5/7 13:19:25", "full_path": "C:\\Windows\\System32\\drivers\\WindowsTrustedRTProxy.sys", "file_attributes": "A", "digital_signature": "WindowsTrustedRTProxy", "certificate_issuer": "@WindowsTrustedRTProxy.inf,%WindowsTrustedRTProxy.SVCDESC%;Microsoft Windows Trusted Runtime Secure Service", "certificate_subject": ""}, {"filename": "pcw.sys", "base_address": "FFFFF800`75D80000", "end_address": "FFFFF800`75D96000", "size": "0x00016000", "load_count": "1", "load_order_group": "31", "driver_type": "Installable Driver", "description": "Performance Counters for Windows Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\drivers\\pcw.sys", "file_attributes": "A", "digital_signature": "pcw", "certificate_issuer": "Performance Counters for Windows Driver", "certificate_subject": ""}, {"filename": "pdc.sys", "base_address": "FFFFF800`75DA0000", "end_address": "FFFFF800`75DD2000", "size": "0x00032000", "load_count": "1", "load_order_group": "32", "driver_type": "System Driver", "description": "Power Dependency Coordinator Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\system32\\drivers\\pdc.sys", "file_attributes": "A", "digital_signature": "pdc", "certificate_issuer": "PDC", "certificate_subject": ""}, {"filename": "CEA.sys", "base_address": "FFFFF800`75DE0000", "end_address": "FFFFF800`75DF8000", "size": "0x00018000", "load_count": "6", "load_order_group": "33", "driver_type": "Dynamic Link Library", "description": "Event Aggregation Kernel Mode Library", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:20", "file_modified_time": "2022/5/7 13:19:20", "full_path": "C:\\Windows\\system32\\drivers\\CEA.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "partmgr.sys", "base_address": "FFFFF800`75E00000", "end_address": "FFFFF800`75E33000", "size": "0x00033000", "load_count": "1", "load_order_group": "34", "driver_type": "System Driver", "description": "Partition driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\drivers\\partmgr.sys", "file_attributes": "A", "digital_signature": "partmgr", "certificate_issuer": "Partition driver", "certificate_subject": ""}, {"filename": "spaceport.sys", "base_address": "FFFFF800`75E40000", "end_address": "FFFFF800`75F22000", "size": "0x000e2000", "load_count": "1", "load_order_group": "35", "driver_type": "System Driver", "description": "Storage Spaces Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\spaceport.sys", "file_attributes": "A", "digital_signature": "spaceport", "certificate_issuer": "@spaceport.inf,%Spaceport_ServiceDesc%;Storage Spaces Driver", "certificate_subject": ""}, {"filename": "volmgr.sys", "base_address": "FFFFF800`75F30000", "end_address": "FFFFF800`75F4C000", "size": "0x0001c000", "load_count": "1", "load_order_group": "36", "driver_type": "System Driver", "description": "Volume Manager Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\volmgr.sys", "file_attributes": "A", "digital_signature": "volmgr", "certificate_issuer": "@volmgr.inf,%volmgr_svcdesc%;Volume Manager Driver", "certificate_subject": ""}, {"filename": "volmgrx.sys", "base_address": "FFFFF800`75F50000", "end_address": "FFFFF800`75FB4000", "size": "0x00064000", "load_count": "1", "load_order_group": "37", "driver_type": "System Driver", "description": "卷管理器扩展驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:20:03", "file_modified_time": "2022/5/7 13:20:03", "full_path": "C:\\Windows\\System32\\drivers\\volmgrx.sys", "file_attributes": "A", "digital_signature": "volmgrx", "certificate_issuer": "Dynamic Volume Manager", "certificate_subject": ""}, {"filename": "mountmgr.sys", "base_address": "FFFFF800`75FC0000", "end_address": "FFFFF800`75FDF000", "size": "0x0001f000", "load_count": "1", "load_order_group": "38", "driver_type": "System Driver", "description": "装载点管理器", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\drivers\\mountmgr.sys", "file_attributes": "A", "digital_signature": "mountmgr", "certificate_issuer": "Mount Point Manager", "certificate_subject": ""}, {"filename": "stornvme.sys", "base_address": "FFFFF800`75FE0000", "end_address": "FFFFF800`7601A000", "size": "0x0003a000", "load_count": "1", "load_order_group": "39", "driver_type": "System Driver", "description": "Microsoft NVM Express Storport Miniport Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\drivers\\stornvme.sys", "file_attributes": "A", "digital_signature": "stornvme", "certificate_issuer": "@stornvme.inf,%StorNVMe_ServiceDesc%;Microsoft Standard NVM Express Driver", "certificate_subject": ""}, {"filename": "storport.sys", "base_address": "FFFFF800`76020000", "end_address": "FFFFF800`7613D000", "size": "0x0011d000", "load_count": "1", "load_order_group": "40", "driver_type": "System Driver", "description": "Microsoft Storage Port Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\drivers\\storport.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "EhStorClass.sys", "base_address": "FFFFF800`76140000", "end_address": "FFFFF800`76164000", "size": "0x00024000", "load_count": "1", "load_order_group": "41", "driver_type": "System Driver", "description": "Enhanced Storage Class driver for IEEE 1667 or TCG OPAL devices", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:20:05", "file_modified_time": "2022/5/7 13:20:05", "full_path": "C:\\Windows\\System32\\drivers\\EhStorClass.sys", "file_attributes": "A", "digital_signature": "EhStorClass", "certificate_issuer": "Enhanced Storage Filter Driver", "certificate_subject": ""}, {"filename": "fileinfo.sys", "base_address": "FFFFF800`76170000", "end_address": "FFFFF800`7618C000", "size": "0x0001c000", "load_count": "1", "load_order_group": "42", "driver_type": "System Driver", "description": "FileInfo Filter Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:33", "file_modified_time": "2022/5/7 13:19:33", "full_path": "C:\\Windows\\System32\\drivers\\fileinfo.sys", "file_attributes": "A", "digital_signature": "FileInfo", "certificate_issuer": "File Information FS MiniFilter", "certificate_subject": ""}, {"filename": "Wof.sys", "base_address": "FFFFF800`76190000", "end_address": "FFFFF800`761D5000", "size": "0x00045000", "load_count": "1", "load_order_group": "43", "driver_type": "System Driver", "description": "Windows 覆盖筛选器", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:47", "file_modified_time": "2025/4/25 22:24:47", "full_path": "C:\\Windows\\System32\\Drivers\\Wof.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Ntfs.sys", "base_address": "FFFFF800`761E0000", "end_address": "FFFFF800`76513000", "size": "0x00333000", "load_count": "1", "load_order_group": "44", "driver_type": "System Driver", "description": "NT 文件系统驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\Drivers\\Ntfs.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Fs_Rec.sys", "base_address": "FFFFF800`76520000", "end_address": "FFFFF800`7652F000", "size": "0x0000f000", "load_count": "1", "load_order_group": "45", "driver_type": "System Driver", "description": "File System Recognizer Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\System32\\Drivers\\Fs_Rec.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "ndis.sys", "base_address": "FFFFF800`76530000", "end_address": "FFFFF800`766C0000", "size": "0x00190000", "load_count": "35", "load_order_group": "46", "driver_type": "Network Driver", "description": "网络驱动程序接口规范(NDIS)", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\system32\\drivers\\ndis.sys", "file_attributes": "A", "digital_signature": "NDIS", "certificate_issuer": "NDIS System Driver", "certificate_subject": ""}, {"filename": "NETIO.SYS", "base_address": "FFFFF800`766D0000", "end_address": "FFFFF800`76772000", "size": "0x000a2000", "load_count": "30", "load_order_group": "47", "driver_type": "Network Driver", "description": "Network I/O Subsystem", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\system32\\drivers\\NETIO.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "ksecpkg.sys", "base_address": "FFFFF800`76780000", "end_address": "FFFFF800`767B6000", "size": "0x00036000", "load_count": "1", "load_order_group": "48", "driver_type": "System Driver", "description": "Kernel Security Support Provider Interface Packages", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:45", "file_modified_time": "2025/4/25 22:24:45", "full_path": "C:\\Windows\\System32\\Drivers\\ksecpkg.sys", "file_attributes": "A", "digital_signature": "KSecPkg", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "tcpip.sys", "base_address": "FFFFF800`767C0000", "end_address": "FFFFF800`76AED000", "size": "0x0032d000", "load_count": "1", "load_order_group": "49", "driver_type": "Network Driver", "description": "TCP/IP 驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:45", "file_modified_time": "2025/4/25 22:24:45", "full_path": "C:\\Windows\\System32\\drivers\\tcpip.sys", "file_attributes": "A", "digital_signature": "Tcpip6", "certificate_issuer": "@todo.dll,-100;Microsoft IPv6 Protocol Driver", "certificate_subject": ""}, {"filename": "fwpkclnt.sys", "base_address": "FFFFF800`76AF0000", "end_address": "FFFFF800`76B73000", "size": "0x00083000", "load_count": "11", "load_order_group": "50", "driver_type": "Dynamic Link Library", "description": "FWP/IPsec Kernel-Mode API", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:45", "file_modified_time": "2025/4/25 22:24:45", "full_path": "C:\\Windows\\System32\\drivers\\fwpkclnt.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "wfplwfs.sys", "base_address": "FFFFF800`76B80000", "end_address": "FFFFF800`76BB1000", "size": "0x00031000", "load_count": "1", "load_order_group": "51", "driver_type": "Network Driver", "description": "WFP NDIS 6.30 Lightweight Filter Driver", "version": "10.0.22621.4249", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:35", "file_modified_time": "2025/4/25 22:24:35", "full_path": "C:\\Windows\\System32\\drivers\\wfplwfs.sys", "file_attributes": "A", "digital_signature": "WFPLWFS", "certificate_issuer": "Microsoft Windows 筛选平台", "certificate_subject": ""}, {"filename": "fvevol.sys", "base_address": "FFFFF800`76BC0000", "end_address": "FFFFF800`76C95000", "size": "0x000d5000", "load_count": "1", "load_order_group": "52", "driver_type": "System Driver", "description": "BitLocker Drive Encryption Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:33", "file_modified_time": "2025/4/25 22:25:33", "full_path": "C:\\Windows\\System32\\DRIVERS\\fvevol.sys", "file_attributes": "A", "digital_signature": "fvevol", "certificate_issuer": "BitLocker Drive Encryption Filter Driver", "certificate_subject": ""}, {"filename": "volume.sys", "base_address": "FFFFF800`76CA0000", "end_address": "FFFFF800`76CAB000", "size": "0x0000b000", "load_count": "1", "load_order_group": "53", "driver_type": "System Driver", "description": "Volume driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:03", "file_modified_time": "2022/5/7 13:19:03", "full_path": "C:\\Windows\\System32\\drivers\\volume.sys", "file_attributes": "A", "digital_signature": "volume", "certificate_issuer": "@volume.inf,%VolumeServiceDesc%;Volume driver", "certificate_subject": ""}, {"filename": "volsnap.sys", "base_address": "FFFFF800`76CB0000", "end_address": "FFFFF800`76D20000", "size": "0x00070000", "load_count": "1", "load_order_group": "54", "driver_type": "System Driver", "description": "卷映像复制驱动程序", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:52", "file_modified_time": "2025/4/25 22:24:52", "full_path": "C:\\Windows\\System32\\drivers\\volsnap.sys", "file_attributes": "A", "digital_signature": "volsnap", "certificate_issuer": "卷映像复制驱动程序", "certificate_subject": ""}, {"filename": "rdyboost.sys", "base_address": "FFFFF800`76D30000", "end_address": "FFFFF800`76D81000", "size": "0x00051000", "load_count": "1", "load_order_group": "55", "driver_type": "System Driver", "description": "ReadyBoost Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:25", "file_modified_time": "2025/4/25 22:25:25", "full_path": "C:\\Windows\\System32\\drivers\\rdyboost.sys", "file_attributes": "A", "digital_signature": "rdyboost", "certificate_issuer": "ReadyBoost", "certificate_subject": ""}, {"filename": "mup.sys", "base_address": "FFFFF800`76D90000", "end_address": "FFFFF800`76DB7000", "size": "0x00027000", "load_count": "4", "load_order_group": "56", "driver_type": "Network Driver", "description": "多个 UNC 提供程序驱动程序", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\Drivers\\mup.sys", "file_attributes": "A", "digital_signature": "<PERSON><PERSON>", "certificate_issuer": "MUP", "certificate_subject": ""}, {"filename": "iorate.sys", "base_address": "FFFFF800`76DC0000", "end_address": "FFFFF800`76DD3000", "size": "0x00013000", "load_count": "1", "load_order_group": "57", "driver_type": "System Driver", "description": "I/O 速率控制筛选器", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:18:58", "file_modified_time": "2022/5/7 13:18:58", "full_path": "C:\\Windows\\system32\\drivers\\iorate.sys", "file_attributes": "A", "digital_signature": "iorate", "certificate_issuer": "磁盘 I/O 速率筛选器驱动程序", "certificate_subject": ""}, {"filename": "disk.sys", "base_address": "FFFFF800`76E00000", "end_address": "FFFFF800`76E20000", "size": "0x00020000", "load_count": "1", "load_order_group": "58", "driver_type": "System Driver", "description": "PnP Disk Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\disk.sys", "file_attributes": "A", "digital_signature": "disk", "certificate_issuer": "@disk.inf,%disk_ServiceDesc%;Disk Driver", "certificate_subject": ""}, {"filename": "CLASSPNP.SYS", "base_address": "FFFFF800`76E30000", "end_address": "FFFFF800`76EA8000", "size": "0x00078000", "load_count": "1", "load_order_group": "59", "driver_type": "System Driver", "description": "SCSI Class System Dll", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\System32\\drivers\\CLASSPNP.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "mrxsmb.sys", "base_address": "FFFFF800`90A00000", "end_address": "FFFFF800`90AA5000", "size": "0x000a5000", "load_count": "2", "load_order_group": "183", "driver_type": "System Driver", "description": "Windows NT SMB Minirdr", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\system32\\DRIVERS\\mrxsmb.sys", "file_attributes": "A", "digital_signature": "mrxsmb", "certificate_issuer": "SMB MiniRedirector Wrapper and Engine", "certificate_subject": ""}, {"filename": "ACE-BOOT.sys", "base_address": "FFFFF800`90AE0000", "end_address": "FFFFF800`90D4C000", "size": "0x0026c000", "load_count": "1", "load_order_group": "61", "driver_type": "Installable Driver", "description": "ACE-BOOT64 NT Driver", "version": "1.0.2506.16634", "company": "ANTICHEATEXPERT.COM", "product_name": "Anti-Cheat Expert", "file_created_time": "2025/7/20 20:48:36", "file_modified_time": "2025/7/20 20:48:36", "full_path": "C:\\Program Files\\AntiCheatExpert\\ACE-BOOT.sys", "file_attributes": "A", "digital_signature": "ACE-BOOT", "certificate_issuer": "ACE-BOOT", "certificate_subject": ""}, {"filename": "filecrypt.sys", "base_address": "FFFFF800`90D50000", "end_address": "FFFFF800`90D66000", "size": "0x00016000", "load_count": "1", "load_order_group": "62", "driver_type": "System Driver", "description": "Windows sandboxing and encryption filter", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:02", "file_modified_time": "2022/5/7 13:19:02", "full_path": "C:\\Windows\\system32\\drivers\\filecrypt.sys", "file_attributes": "A", "digital_signature": "FileCrypt", "certificate_issuer": "FileCrypt", "certificate_subject": ""}, {"filename": "tbs.sys", "base_address": "FFFFF800`90D70000", "end_address": "FFFFF800`90D80000", "size": "0x00010000", "load_count": "1", "load_order_group": "63", "driver_type": "System Driver", "description": "Export driver for kernel mode TPM API", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:25", "file_modified_time": "2025/4/25 22:24:25", "full_path": "C:\\Windows\\system32\\drivers\\tbs.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "UCPD.sys", "base_address": "FFFFF800`90D90000", "end_address": "FFFFF800`90DB1000", "size": "0x00021000", "load_count": "1", "load_order_group": "64", "driver_type": "System Driver", "description": "User Choice Protection Driver", "version": "*******", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:21", "file_modified_time": "2025/4/25 22:24:21", "full_path": "C:\\Windows\\system32\\drivers\\UCPD.sys", "file_attributes": "A", "digital_signature": "UCPD", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Null.SYS", "base_address": "FFFFF800`90DC0000", "end_address": "FFFFF800`90DCB000", "size": "0x0000b000", "load_count": "1", "load_order_group": "65", "driver_type": "System Driver", "description": "NULL Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:30", "file_modified_time": "2022/5/7 13:19:30", "full_path": "C:\\Windows\\System32\\Drivers\\Null.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "ipf_lf.sys", "base_address": "FFFFF800`90DD0000", "end_address": "FFFFF800`90E4A000", "size": "0x0007a000", "load_count": "1", "load_order_group": "174", "driver_type": "System Driver", "description": "Intel(R) Innovation Platform Framework Manager Participant", "version": "1.0.11900.1682", "company": "Intel Corporation", "product_name": "Intel(R) Innovation Platform Framework", "file_created_time": "2024/8/2 11:04:50", "file_modified_time": "2025/7/12 23:48:04", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\ipf_cpu.inf_amd64_6f6b1ffac2a6a08b\\ipf_lf.sys", "file_attributes": "", "digital_signature": "ipf_lf", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "nwifi.sys", "base_address": "FFFFF800`90E50000", "end_address": "FFFFF800`90F0A000", "size": "0x000ba000", "load_count": "1", "load_order_group": "180", "driver_type": "Network Driver", "description": "NativeWiFi 微型端口驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:26", "file_modified_time": "2025/4/25 22:24:26", "full_path": "C:\\Windows\\system32\\DRIVERS\\nwifi.sys", "file_attributes": "A", "digital_signature": "NativeWifiP", "certificate_issuer": "NativeWiFi 筛选器", "certificate_subject": ""}, {"filename": "bowser.sys", "base_address": "FFFFF800`90F10000", "end_address": "FFFFF800`90F36000", "size": "0x00026000", "load_count": "1", "load_order_group": "181", "driver_type": "System Driver", "description": "NT Lan Manager Datagram Receiver Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:10", "file_modified_time": "2022/5/7 13:19:10", "full_path": "C:\\Windows\\system32\\DRIVERS\\bowser.sys", "file_attributes": "A", "digital_signature": "bowser", "certificate_issuer": "浏览器", "certificate_subject": ""}, {"filename": "crashdmp.sys", "base_address": "FFFFF800`90FA0000", "end_address": "FFFFF800`90FC8000", "size": "0x00028000", "load_count": "1", "load_order_group": "60", "driver_type": "System Driver", "description": "Crash Dump Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\Drivers\\crashdmp.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "CimFS.SYS", "base_address": "FFFFF800`93E00000", "end_address": "FFFFF800`93E28000", "size": "0x00028000", "load_count": "1", "load_order_group": "72", "driver_type": "Dynamic Link Library", "description": "CimFS driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:37", "file_modified_time": "2025/4/25 22:24:37", "full_path": "C:\\Windows\\System32\\Drivers\\CimFS.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "tdx.sys", "base_address": "FFFFF800`93E30000", "end_address": "FFFFF800`93E54000", "size": "0x00024000", "load_count": "1", "load_order_group": "73", "driver_type": "Network Driver", "description": "TDI Translation Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:10", "file_modified_time": "2025/4/25 22:25:10", "full_path": "C:\\Windows\\system32\\DRIVERS\\tdx.sys", "file_attributes": "A", "digital_signature": "tdx", "certificate_issuer": "NetIO 旧 TDI 支持驱动程序", "certificate_subject": ""}, {"filename": "TDI.SYS", "base_address": "FFFFF800`93E60000", "end_address": "FFFFF800`93E71000", "size": "0x00011000", "load_count": "7", "load_order_group": "74", "driver_type": "Network Driver", "description": "TDI Wrapper", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:31", "file_modified_time": "2022/5/7 13:19:31", "full_path": "C:\\Windows\\system32\\DRIVERS\\TDI.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "nfwfp.sys", "base_address": "FFFFF800`93E80000", "end_address": "FFFFF800`93E9B000", "size": "0x0001b000", "load_count": "1", "load_order_group": "75", "driver_type": "Network Driver", "description": "Leigod WFP Driver", "version": "*******", "company": "Windows (R) Win 7 DDK provider", "product_name": "nfwfp", "file_created_time": "2025/7/17 4:45:02", "file_modified_time": "2025/7/17 4:45:02", "full_path": "C:\\Windows\\system32\\drivers\\nfwfp.sys", "file_attributes": "A", "digital_signature": "nfwfp", "certificate_issuer": "nfwfp", "certificate_subject": ""}, {"filename": "netbt.sys", "base_address": "FFFFF800`93EA0000", "end_address": "FFFFF800`93EF1000", "size": "0x00051000", "load_count": "1", "load_order_group": "76", "driver_type": "System Driver", "description": "MBT Transport driver", "version": "10.0.22621.5185", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:54", "file_modified_time": "2025/4/25 22:24:54", "full_path": "C:\\Windows\\System32\\DRIVERS\\netbt.sys", "file_attributes": "A", "digital_signature": "NetBT", "certificate_issuer": "NETBT", "certificate_subject": ""}, {"filename": "afd.sys", "base_address": "FFFFF800`93F00000", "end_address": "FFFFF800`93FA9000", "size": "0x000a9000", "load_count": "1", "load_order_group": "77", "driver_type": "System Driver", "description": "WinSock 的辅助功能驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\system32\\drivers\\afd.sys", "file_attributes": "A", "digital_signature": "AFD", "certificate_issuer": "Ancillary Function Driver for Winsock", "certificate_subject": ""}, {"filename": "vwififlt.sys", "base_address": "FFFFF800`93FB0000", "end_address": "FFFFF800`93FCB000", "size": "0x0001b000", "load_count": "2", "load_order_group": "78", "driver_type": "Network Driver", "description": "Virtual WiFi Filter Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:11", "file_modified_time": "2022/5/7 13:19:11", "full_path": "C:\\Windows\\System32\\drivers\\vwififlt.sys", "file_attributes": "A", "digital_signature": "vwififlt", "certificate_issuer": "Virtual WiFi Filter Driver", "certificate_subject": ""}, {"filename": "pacer.sys", "base_address": "FFFFF800`93FD0000", "end_address": "FFFFF800`93FFB000", "size": "0x0002b000", "load_count": "1", "load_order_group": "79", "driver_type": "Network Driver", "description": "QoS 数据包计划程序", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:35", "file_modified_time": "2025/4/25 22:24:35", "full_path": "C:\\Windows\\System32\\drivers\\pacer.sys", "file_attributes": "A", "digital_signature": "Psched", "certificate_issuer": "QoS 数据包计划程序", "certificate_subject": ""}, {"filename": "ndiscap.sys", "base_address": "FFFFF800`94000000", "end_address": "FFFFF800`94015000", "size": "0x00015000", "load_count": "1", "load_order_group": "80", "driver_type": "Network Driver", "description": "Microsoft NDIS Packet Capture Filter Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:20:14", "file_modified_time": "2022/5/7 13:20:14", "full_path": "C:\\Windows\\System32\\drivers\\ndiscap.sys", "file_attributes": "A", "digital_signature": "NdisCap", "certificate_issuer": "Microsoft NDIS 捕获", "certificate_subject": ""}, {"filename": "netbios.sys", "base_address": "FFFFF800`94020000", "end_address": "FFFFF800`94035000", "size": "0x00015000", "load_count": "1", "load_order_group": "81", "driver_type": "Network Driver", "description": "NetBIOS interface driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:54", "file_modified_time": "2025/4/25 22:24:54", "full_path": "C:\\Windows\\system32\\drivers\\netbios.sys", "file_attributes": "A", "digital_signature": "NetBIOS", "certificate_issuer": "NetBIOS Interface", "certificate_subject": ""}, {"filename": "Vid.sys", "base_address": "FFFFF800`94040000", "end_address": "FFFFF800`94109000", "size": "0x000c9000", "load_count": "1", "load_order_group": "82", "driver_type": "System Driver", "description": "Microsoft Hyper-V Virtualization Infrastructure Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:10", "file_modified_time": "2025/4/25 22:25:10", "full_path": "C:\\Windows\\System32\\drivers\\Vid.sys", "file_attributes": "A", "digital_signature": "Vid", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "winhvr.sys", "base_address": "FFFFF800`94110000", "end_address": "FFFFF800`94136000", "size": "0x00026000", "load_count": "1", "load_order_group": "83", "driver_type": "System Driver", "description": "Windows Hypervisor Root Interface Driver", "version": "10.0.22621.3374", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:10", "file_modified_time": "2025/4/25 22:25:10", "full_path": "C:\\Windows\\System32\\drivers\\winhvr.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "rdbss.sys", "base_address": "FFFFF800`94140000", "end_address": "FFFFF800`941BD000", "size": "0x0007d000", "load_count": "4", "load_order_group": "84", "driver_type": "System Driver", "description": "已重定向驱动器缓冲子系统驱动程序", "version": "10.0.22621.4974", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\system32\\DRIVERS\\rdbss.sys", "file_attributes": "A", "digital_signature": "rdbss", "certificate_issuer": "Redirected Buffering Sub System", "certificate_subject": ""}, {"filename": "csc.sys", "base_address": "FFFFF800`941C0000", "end_address": "FFFFF800`94255000", "size": "0x00095000", "load_count": "1", "load_order_group": "85", "driver_type": "System Driver", "description": "Windows Client Side Caching Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:25:34", "file_modified_time": "2025/4/25 22:25:34", "full_path": "C:\\Windows\\system32\\drivers\\csc.sys", "file_attributes": "A", "digital_signature": "CSC", "certificate_issuer": "Offline Files Driver", "certificate_subject": ""}, {"filename": "nsiproxy.sys", "base_address": "FFFFF800`94260000", "end_address": "FFFFF800`94273000", "size": "0x00013000", "load_count": "1", "load_order_group": "86", "driver_type": "Network Driver", "description": "NSI Proxy", "version": "10.0.22621.4746", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\system32\\drivers\\nsiproxy.sys", "file_attributes": "A", "digital_signature": "nsiproxy", "certificate_issuer": "NSI Proxy Service Driver", "certificate_subject": ""}, {"filename": "npsvctrig.sys", "base_address": "FFFFF800`94280000", "end_address": "FFFFF800`94290000", "size": "0x00010000", "load_count": "1", "load_order_group": "87", "driver_type": "System Driver", "description": "Named pipe service triggers", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:32", "file_modified_time": "2022/5/7 13:19:32", "full_path": "C:\\Windows\\System32\\drivers\\npsvctrig.sys", "file_attributes": "A", "digital_signature": "npsvctrig", "certificate_issuer": "@npsvctrig.inf,%NPSVCTRIG.SvcDisplayName%;Named pipe service trigger provider", "certificate_subject": ""}, {"filename": "mssmbios.sys", "base_address": "FFFFF800`942A0000", "end_address": "FFFFF800`942B1000", "size": "0x00011000", "load_count": "1", "load_order_group": "88", "driver_type": "System Driver", "description": "System Management BIOS Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:04", "file_modified_time": "2022/5/7 13:19:04", "full_path": "C:\\Windows\\System32\\drivers\\mssmbios.sys", "file_attributes": "A", "digital_signature": "mssmbios", "certificate_issuer": "@mssmbios.inf,%mssmbios_svcdesc%;Microsoft System Management BIOS Driver", "certificate_subject": ""}, {"filename": "dfsc.sys", "base_address": "FFFFF800`942C0000", "end_address": "FFFFF800`942EE000", "size": "0x0002e000", "load_count": "1", "load_order_group": "89", "driver_type": "System Driver", "description": "DFS Namespace Client Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:48", "file_modified_time": "2025/4/25 22:24:48", "full_path": "C:\\Windows\\System32\\Drivers\\dfsc.sys", "file_attributes": "A", "digital_signature": "Dfsc", "certificate_issuer": "DFS Namespace Client Driver", "certificate_subject": ""}, {"filename": "dam.sys", "base_address": "FFFFF800`942F0000", "end_address": "FFFFF800`94310000", "size": "0x00020000", "load_count": "1", "load_order_group": "90", "driver_type": "System Driver", "description": "DAM Kernel Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:52", "file_modified_time": "2025/4/25 22:24:52", "full_path": "C:\\Windows\\system32\\drivers\\dam.sys", "file_attributes": "A", "digital_signature": "dam", "certificate_issuer": "Desktop Activity Moderator Driver", "certificate_subject": ""}, {"filename": "KMPDC.sys", "base_address": "FFFFF800`94320000", "end_address": "FFFFF800`9432F000", "size": "0x0000f000", "load_count": "1", "load_order_group": "91", "driver_type": "System Driver", "description": "Kernel Mode Power Dependency Coordinator", "version": "10.0.22621.5185", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\system32\\drivers\\KMPDC.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "fastfat.SYS", "base_address": "FFFFF800`94330000", "end_address": "FFFFF800`9439F000", "size": "0x0006f000", "load_count": "1", "load_order_group": "92", "driver_type": "System Driver", "description": "Fast FAT File System Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:20", "file_modified_time": "2025/4/25 22:24:20", "full_path": "C:\\Windows\\System32\\Drivers\\fastfat.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "ahcache.sys", "base_address": "FFFFF800`943A0000", "end_address": "FFFFF800`943FC000", "size": "0x0005c000", "load_count": "1", "load_order_group": "93", "driver_type": "System Driver", "description": "Application Compatibility Cache", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:52", "file_modified_time": "2025/4/25 22:24:52", "full_path": "C:\\Windows\\system32\\DRIVERS\\ahcache.sys", "file_attributes": "A", "digital_signature": "ahcache", "certificate_issuer": "Application Compatibility Cache", "certificate_subject": ""}, {"filename": "CompositeBus.sys", "base_address": "FFFFF800`94400000", "end_address": "FFFFF800`94414000", "size": "0x00014000", "load_count": "1", "load_order_group": "94", "driver_type": "Dynamic Link Library", "description": "Multi-Transport Composite Bus Enumerator", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:01", "file_modified_time": "2022/5/7 13:19:01", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\compositebus.inf_amd64_2e50c98177d80a40\\CompositeBus.sys", "file_attributes": "A", "digital_signature": "CompositeBus", "certificate_issuer": "@compositebus.inf,%CompositeBus.SVCDESC%;Composite Bus Enumerator Driver", "certificate_subject": ""}, {"filename": "kdnic.sys", "base_address": "FFFFF800`94420000", "end_address": "FFFFF800`9442F000", "size": "0x0000f000", "load_count": "1", "load_order_group": "95", "driver_type": "System Driver", "description": "Microsoft Kernel Debugger Network Miniport", "version": "*******", "company": "Microsoft Corporation", "product_name": "Microsoft Kernel Debugger Network Adapter (NDIS 6.20 Miniport)", "file_created_time": "2022/5/7 13:19:31", "file_modified_time": "2022/5/7 13:19:31", "full_path": "C:\\Windows\\System32\\drivers\\kdnic.sys", "file_attributes": "A", "digital_signature": "kdnic", "certificate_issuer": "@kdnic.inf,%KdNic.Service.DispName%;Microsoft Kernel Debug Network Miniport (NDIS 6.20)", "certificate_subject": ""}, {"filename": "CAD.sys", "base_address": "FFFFF800`94430000", "end_address": "FFFFF800`94446000", "size": "0x00016000", "load_count": "1", "load_order_group": "96", "driver_type": "System Driver", "description": "Charge Arbiration Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:01", "file_modified_time": "2022/5/7 13:19:01", "full_path": "C:\\Windows\\System32\\drivers\\CAD.sys", "file_attributes": "A", "digital_signature": "CAD", "certificate_issuer": "@ChargeArbitration.inf,%CAD_DevDesc%;Charge Arbitration Driver", "certificate_subject": ""}, {"filename": "wanarp.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`9446F000", "size": "0x0001f000", "load_count": "1", "load_order_group": "178", "driver_type": "Network Driver", "description": "MS Remote Access and Routing ARP Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\DRIVERS\\wanarp.sys", "file_attributes": "A", "digital_signature": "wanarpv6", "certificate_issuer": "远程访问 IPv6 ARP 驱动程序", "certificate_subject": ""}, {"filename": "ndisuio.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00019000", "load_count": "1", "load_order_group": "179", "driver_type": "Network Driver", "description": "NDIS 用户模式 I/O 驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:31", "file_modified_time": "2022/5/7 13:19:31", "full_path": "C:\\Windows\\system32\\drivers\\ndisuio.sys", "file_attributes": "A", "digital_signature": "<PERSON><PERSON><PERSON><PERSON>", "certificate_issuer": "NDIS Usermode I/O Protocol", "certificate_subject": ""}, {"filename": "mpsdrv.sys", "base_address": "FFFFF800`94490000", "end_address": "FFFFF800`944AB000", "size": "0x0001b000", "load_count": "1", "load_order_group": "182", "driver_type": "System Driver", "description": "Microsoft Protection Service Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:29", "file_modified_time": "2025/4/25 22:24:29", "full_path": "C:\\Windows\\System32\\drivers\\mpsdrv.sys", "file_attributes": "A", "digital_signature": "mpsdrv", "certificate_issuer": "Windows Defender Firewall Authorization Driver", "certificate_subject": ""}, {"filename": "IntcBTAu.sys", "base_address": "FFFFF800`944B0000", "end_address": "FFFFF800`9458A000", "size": "0x000da000", "load_count": "1", "load_order_group": "147", "driver_type": "Sound Driver", "description": "Intel(R) Smart Sound Technology for Bluetooth(R) Audio", "version": "10.29.0.9467", "company": "Intel(R) Corporation", "product_name": "Intel(R) Smart Sound Technology for Bluetooth(R) Audio", "file_created_time": "2023/7/25 11:41:20", "file_modified_time": "2025/7/12 23:48:13", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\intcbtau.inf_amd64_28a2e1decdcf0cae\\IntcBTAu.sys", "file_attributes": "", "digital_signature": "IntcBTAu", "certificate_issuer": "@oem75.inf,%IntcAud.SvcDesc%;适用于蓝牙? 音频的英特尔? 智音技术", "certificate_subject": ""}, {"filename": "IntcDMic.sys", "base_address": "FFFFF800`94590000", "end_address": "FFFFF800`9464C000", "size": "0x000bc000", "load_count": "1", "load_order_group": "148", "driver_type": "Sound Driver", "description": "Intel(R) Smart Sound Technology for Digital Microphones", "version": "10.29.0.9467", "company": "Intel(R) Corporation", "product_name": "Intel(R) Smart Sound Technology for Digital Microphones", "file_created_time": "2023/7/25 11:41:22", "file_modified_time": "2025/7/12 23:48:14", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\intcdmic.inf_amd64_1e0ae896c464811e\\IntcDMic.sys", "file_attributes": "", "digital_signature": "IntcDMic", "certificate_issuer": "@oem74.inf,%IntcAud.SvcDesc%;适用于数字麦克风的英特尔? 智音技术", "certificate_subject": ""}, {"filename": "usbccgp.sys", "base_address": "FFFFF800`94650000", "end_address": "FFFFF800`94686000", "size": "0x00036000", "load_count": "1", "load_order_group": "149", "driver_type": "Dynamic Link Library", "description": "USB Common Class Generic Parent Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\usbccgp.sys", "file_attributes": "A", "digital_signature": "usbccgp", "certificate_issuer": "@usb.inf,%GenericParent.SvcDesc%;Microsoft USB Generic Parent Driver", "certificate_subject": ""}, {"filename": "WUDFRd.sys", "base_address": "FFFFF800`94690000", "end_address": "FFFFF800`946E7000", "size": "0x00057000", "load_count": "1", "load_order_group": "166", "driver_type": "System Driver", "description": "Windows Driver Foundation - User-mode Driver Framework Reflector", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\System32\\drivers\\WUDFRd.sys", "file_attributes": "A", "digital_signature": "WUDFWpdMtp", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "wcifs.sys", "base_address": "FFFFF800`946F0000", "end_address": "FFFFF800`9472B000", "size": "0x0003b000", "load_count": "1", "load_order_group": "168", "driver_type": "System Driver", "description": "Windows Container Isolation FS Filter Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\system32\\drivers\\wcifs.sys", "file_attributes": "A", "digital_signature": "wcifs", "certificate_issuer": "Windows Container Isolation", "certificate_subject": ""}, {"filename": "cldflt.sys", "base_address": "FFFFF800`94730000", "end_address": "FFFFF800`947BC000", "size": "0x0008c000", "load_count": "1", "load_order_group": "169", "driver_type": "System Driver", "description": "Cloud Files Mini Filter Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:47", "file_modified_time": "2025/4/25 22:24:47", "full_path": "C:\\Windows\\system32\\drivers\\cldflt.sys", "file_attributes": "A", "digital_signature": "CldFlt", "certificate_issuer": "Windows Cloud Files Filter Driver", "certificate_subject": ""}, {"filename": "mmcss.sys", "base_address": "FFFFF800`947C0000", "end_address": "FFFFF800`947D5000", "size": "0x00015000", "load_count": "1", "load_order_group": "171", "driver_type": "System Driver", "description": "MMCSS Driver", "version": "10.0.22621.3374", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:21", "file_modified_time": "2025/4/25 22:24:21", "full_path": "C:\\Windows\\system32\\drivers\\mmcss.sys", "file_attributes": "A", "digital_signature": "MMCSS", "certificate_issuer": "Multimedia Class Scheduler", "certificate_subject": ""}, {"filename": "bindflt.sys", "base_address": "FFFFF800`947E0000", "end_address": "FFFFF800`9480A000", "size": "0x0002a000", "load_count": "1", "load_order_group": "172", "driver_type": "System Driver", "description": "Windows Bind Filter Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\system32\\drivers\\bindflt.sys", "file_attributes": "A", "digital_signature": "bindflt", "certificate_issuer": "Windows Bind Filter Driver", "certificate_subject": ""}, {"filename": "ipf_cpu.sys", "base_address": "FFFFF800`94830000", "end_address": "FFFFF800`94845000", "size": "0x00015000", "load_count": "1", "load_order_group": "173", "driver_type": "System Driver", "description": "Intel(R) Innovation Platform Framework CPU Participant", "version": "1.0.11900.1682", "company": "Intel Corporation", "product_name": "Intel(R) Innovation Platform Framework", "file_created_time": "2024/8/2 11:04:48", "file_modified_time": "2025/7/12 23:48:04", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\ipf_cpu.inf_amd64_6f6b1ffac2a6a08b\\ipf_cpu.sys", "file_attributes": "", "digital_signature": "ipf_cpu", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "lltdio.sys", "base_address": "FFFFF800`94850000", "end_address": "FFFFF800`94869000", "size": "0x00019000", "load_count": "1", "load_order_group": "175", "driver_type": "Network Driver", "description": "Link-Layer Topology Mapper I/O Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:50", "file_modified_time": "2025/4/25 22:24:50", "full_path": "C:\\Windows\\system32\\drivers\\lltdio.sys", "file_attributes": "A", "digital_signature": "lltdio", "certificate_issuer": "链路层拓扑发现映射器 I/O 驱动程序", "certificate_subject": ""}, {"filename": "mslldp.sys", "base_address": "FFFFF800`94870000", "end_address": "FFFFF800`94889000", "size": "0x00019000", "load_count": "1", "load_order_group": "176", "driver_type": "Network Driver", "description": "Microsoft 链路层发现协议驱动程序", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:54", "file_modified_time": "2025/4/25 22:24:54", "full_path": "C:\\Windows\\system32\\drivers\\mslldp.sys", "file_attributes": "A", "digital_signature": "MsLldp", "certificate_issuer": "Microsoft 链路层发现协议", "certificate_subject": ""}, {"filename": "dxgkrnl.sys", "base_address": "FFFFF800`948A0000", "end_address": "FFFFF800`94D2C000", "size": "0x0048c000", "load_count": "16", "load_order_group": "66", "driver_type": "System Driver", "description": "DirectX Graphics Kernel", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:31", "file_modified_time": "2025/4/25 22:24:31", "full_path": "C:\\Windows\\System32\\drivers\\dxgkrnl.sys", "file_attributes": "A", "digital_signature": "DXGKrnl", "certificate_issuer": "LDDM Graphics Subsystem", "certificate_subject": ""}, {"filename": "watchdog.sys", "base_address": "FFFFF800`94D30000", "end_address": "FFFFF800`94D52000", "size": "0x00022000", "load_count": "11", "load_order_group": "67", "driver_type": "Dynamic Link Library", "description": "Watchdog Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:31", "file_modified_time": "2025/4/25 22:24:31", "full_path": "C:\\Windows\\System32\\drivers\\watchdog.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "BasicDisplay.sys", "base_address": "FFFFF800`94D60000", "end_address": "FFFFF800`94D77000", "size": "0x00017000", "load_count": "1", "load_order_group": "68", "driver_type": "Display Driver", "description": "Microsoft Basic Display Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:05", "file_modified_time": "2022/5/7 13:19:05", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\basicdisplay.inf_amd64_02da009b3d736cc1\\BasicDisplay.sys", "file_attributes": "A", "digital_signature": "BasicDisplay", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "BasicRender.sys", "base_address": "FFFFF800`94D80000", "end_address": "FFFFF800`94D92000", "size": "0x00012000", "load_count": "1", "load_order_group": "69", "driver_type": "Display Driver", "description": "Microsoft Basic Render Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\basicrender.inf_amd64_402645b3f1a80dd7\\BasicRender.sys", "file_attributes": "A", "digital_signature": "BasicRender", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Npfs.SYS", "base_address": "FFFFF800`94DA0000", "end_address": "FFFFF800`94DBC000", "size": "0x0001c000", "load_count": "1", "load_order_group": "70", "driver_type": "System Driver", "description": "NPFS Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\System32\\Drivers\\Npfs.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "Msfs.SYS", "base_address": "FFFFF800`94DC0000", "end_address": "FFFFF800`94DD2000", "size": "0x00012000", "load_count": "1", "load_order_group": "71", "driver_type": "System Driver", "description": "Mailslot driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:44", "file_modified_time": "2025/4/25 22:24:44", "full_path": "C:\\Windows\\System32\\Drivers\\Msfs.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "rspndr.sys", "base_address": "FFFFF800`94DE0000", "end_address": "FFFFF800`94DFD000", "size": "0x0001d000", "load_count": "1", "load_order_group": "177", "driver_type": "Network Driver", "description": "Link-Layer Topology Responder Driver for NDIS 6", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:50", "file_modified_time": "2025/4/25 22:24:50", "full_path": "C:\\Windows\\system32\\drivers\\rspndr.sys", "file_attributes": "A", "digital_signature": "rspndr", "certificate_issuer": "链路层拓扑发现响应程序", "certificate_subject": ""}, {"filename": "HTTP.sys", "base_address": "FFFFF800`9A600000", "end_address": "FFFFF800`9A7A6000", "size": "0x001a6000", "load_count": "1", "load_order_group": "193", "driver_type": "System Driver", "description": "HTTP 协议堆栈", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:43", "file_modified_time": "2025/4/25 22:24:43", "full_path": "C:\\Windows\\system32\\drivers\\HTTP.sys", "file_attributes": "A", "digital_signature": "HTTP", "certificate_issuer": "HTTP Service", "certificate_subject": ""}, {"filename": "usbaudio.sys", "base_address": "FFFFF800`9A7B0000", "end_address": "FFFFF800`9A7F7000", "size": "0x00047000", "load_count": "1", "load_order_group": "194", "driver_type": "Dynamic Link Library", "description": "USB Audio Class Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:21", "file_modified_time": "2025/4/25 22:24:21", "full_path": "C:\\Windows\\system32\\drivers\\usbaudio.sys", "file_attributes": "A", "digital_signature": "<PERSON><PERSON><PERSON><PERSON>", "certificate_issuer": "@wdma_usb.inf,%USBAudio.SvcDesc%;USB Audio Driver (WDM)", "certificate_subject": ""}, {"filename": "rassstp.sys", "base_address": "FFFFF800`9A800000", "end_address": "FFFFF800`9A81E000", "size": "0x0001e000", "load_count": "1", "load_order_group": "195", "driver_type": "Network Driver", "description": "RAS SSTP Miniport Call Manager", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\drivers\\rassstp.sys", "file_attributes": "A", "digital_signature": "RasSstp", "certificate_issuer": "WAN 微型端口(SSTP)", "certificate_subject": ""}, {"filename": "NDProxy.sys", "base_address": "FFFFF800`9A820000", "end_address": "FFFFF800`9A83E000", "size": "0x0001e000", "load_count": "1", "load_order_group": "196", "driver_type": "System Driver", "description": "NDIS Proxy", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\DRIVERS\\NDProxy.sys", "file_attributes": "A", "digital_signature": "ndproxy", "certificate_issuer": "NDIS Proxy Driver", "certificate_subject": ""}, {"filename": "AgileVpn.sys", "base_address": "FFFFF800`9A840000", "end_address": "FFFFF800`9A86A000", "size": "0x0002a000", "load_count": "1", "load_order_group": "197", "driver_type": "Network Driver", "description": "RAS Agile Vpn Miniport Call Manager", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\drivers\\AgileVpn.sys", "file_attributes": "A", "digital_signature": "RasAgileVpn", "certificate_issuer": "@netavpna.inf,%Svc-Mp-AgileVpn-DispName%;WAN Miniport (IKEv2)", "certificate_subject": ""}, {"filename": "rasl2tp.sys", "base_address": "FFFFF800`9A870000", "end_address": "FFFFF800`9A894000", "size": "0x00024000", "load_count": "1", "load_order_group": "198", "driver_type": "Network Driver", "description": "RAS L2TP mini-port/call-manager driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\drivers\\rasl2tp.sys", "file_attributes": "A", "digital_signature": "Rasl2tp", "certificate_issuer": "WAN 微型端口(L2TP)", "certificate_subject": ""}, {"filename": "raspptp.sys", "base_address": "FFFFF800`9A8A0000", "end_address": "FFFFF800`9A8C2000", "size": "0x00022000", "load_count": "1", "load_order_group": "199", "driver_type": "Network Driver", "description": "Peer-to-Peer Tunneling Protocol", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\drivers\\raspptp.sys", "file_attributes": "A", "digital_signature": "PptpMiniport", "certificate_issuer": "WAN 微型端口(PPTP)", "certificate_subject": ""}, {"filename": "raspppoe.sys", "base_address": "FFFFF800`9A8D0000", "end_address": "FFFFF800`9A8EE000", "size": "0x0001e000", "load_count": "1", "load_order_group": "200", "driver_type": "Network Driver", "description": "RAS PPPoE mini-port/call-manager driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\DRIVERS\\raspppoe.sys", "file_attributes": "A", "digital_signature": "RasPppoe", "certificate_issuer": "远程访问 PPPOE 驱动程序", "certificate_subject": ""}, {"filename": "ndistapi.sys", "base_address": "FFFFF800`9A8F0000", "end_address": "FFFFF800`9A900000", "size": "0x00010000", "load_count": "2", "load_order_group": "201", "driver_type": "Network Driver", "description": "NDIS 3.0 connection wrapper driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\DRIVERS\\ndistapi.sys", "file_attributes": "A", "digital_signature": "NdisTapi", "certificate_issuer": "远程访问 NDIS TAPI 驱动程序", "certificate_subject": ""}, {"filename": "ndiswan.sys", "base_address": "FFFFF800`9A910000", "end_address": "FFFFF800`9A94B000", "size": "0x0003b000", "load_count": "1", "load_order_group": "202", "driver_type": "Network Driver", "description": "MS PPP Framing Driver (Strong Encryption)", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:55", "file_modified_time": "2025/4/25 22:24:55", "full_path": "C:\\Windows\\System32\\drivers\\ndiswan.sys", "file_attributes": "A", "digital_signature": "ndiswanlegacy", "certificate_issuer": "远程访问旧版 NDIS WAN 驱动程序", "certificate_subject": ""}, {"filename": "AC57BD2B90.sys", "base_address": "FFFFF800`9A970000", "end_address": "FFFFF800`9B582000", "size": "0x00c12000", "load_count": "1", "load_order_group": "192", "driver_type": "Unknown", "description": "", "version": "", "company": "", "product_name": "", "file_created_time": "N/A", "file_modified_time": "N/A", "full_path": "C:\\Windows\\SysWOW64\\AC57BD2B90.sys", "file_attributes": "", "digital_signature": "AC57BD2B90", "certificate_issuer": "AC57BD2B90", "certificate_subject": ""}, {"filename": "ks.sys", "base_address": "FFFFF800`AC600000", "end_address": "FFFFF800`AC686000", "size": "0x00086000", "load_count": "6", "load_order_group": "101", "driver_type": "Driver", "description": "Kernel CSA Library", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:51", "file_modified_time": "2025/4/25 22:24:51", "full_path": "C:\\Windows\\System32\\drivers\\ks.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "rt640x64.sys", "base_address": "FFFFF800`AC690000", "end_address": "FFFFF800`AC7EB000", "size": "0x0015b000", "load_count": "1", "load_order_group": "112", "driver_type": "Network Driver", "description": "Realtek 8125/8126/8136/8168/8169 NDIS 6.40 64-bit Driver", "version": "10.75.324.2025", "company": "Realtek", "product_name": "Realtek 8125/8126/8136/8168/8169 PCI/PCIe Adapters", "file_created_time": "2025/5/20 3:09:50", "file_modified_time": "2025/7/12 23:48:02", "full_path": "C:\\Windows\\System32\\drivers\\rt640x64.sys", "file_attributes": "A", "digital_signature": "rt640x64", "certificate_issuer": "@oem78.inf,%rt640.Service.DispName%;Realtek RT640 NT Driver", "certificate_subject": ""}, {"filename": "HIDPARSE.SYS", "base_address": "FFFFF800`AC7F0000", "end_address": "FFFFF800`AC806000", "size": "0x00016000", "load_count": "7", "load_order_group": "125", "driver_type": "Dynamic Link Library", "description": "Hid Parsing Library", "version": "10.0.22621.4111", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\HIDPARSE.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "msgpiowin32.sys", "base_address": "FFFFF800`AC810000", "end_address": "FFFFF800`AC825000", "size": "0x00015000", "load_count": "1", "load_order_group": "126", "driver_type": "System Driver", "description": "GP<PERSON> <PERSON><PERSON> Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:51", "file_modified_time": "2025/4/25 22:24:51", "full_path": "C:\\Windows\\System32\\drivers\\msgpiowin32.sys", "file_attributes": "A", "digital_signature": "msgpiowin32", "certificate_issuer": "@msgpiowin32.inf,%GPIO.SvcDesc%;Common Driver for Buttons, DockMode and Laptop/Slate Indicator", "certificate_subject": ""}, {"filename": "CmBatt.sys", "base_address": "FFFFF800`AC830000", "end_address": "FFFFF800`AC842000", "size": "0x00012000", "load_count": "1", "load_order_group": "127", "driver_type": "System Driver", "description": "Control Method Battery Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:04", "file_modified_time": "2022/5/7 13:19:04", "full_path": "C:\\Windows\\System32\\drivers\\CmBatt.sys", "file_attributes": "A", "digital_signature": "CmBatt", "certificate_issuer": "@cmbatt.inf,%CmBatt.SvcDesc%;Microsoft ACPI Control Method Battery Driver", "certificate_subject": ""}, {"filename": "BATTC.SYS", "base_address": "FFFFF800`AC850000", "end_address": "FFFFF800`AC868000", "size": "0x00018000", "load_count": "1", "load_order_group": "128", "driver_type": "System Driver", "description": "Battery Class Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:04", "file_modified_time": "2022/5/7 13:19:04", "full_path": "C:\\Windows\\System32\\drivers\\BATTC.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "UcmUcsiAcpiClient.sys", "base_address": "FFFFF800`AC870000", "end_address": "FFFFF800`AC885000", "size": "0x00015000", "load_count": "1", "load_order_group": "129", "driver_type": "System Driver", "description": "UCM-UCSI ACPI Client Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\UcmUcsiAcpiClient.sys", "file_attributes": "A", "digital_signature": "UcmUcsiAcpiClient", "certificate_issuer": "@UcmUcsiAcpiClient.inf,%UcmUcsiAcpiClient.ServiceName%;UCM-UCSI ACPI Client", "certificate_subject": ""}, {"filename": "UcmUcsiCx.sys", "base_address": "FFFFF800`AC890000", "end_address": "FFFFF800`AC8C2000", "size": "0x00032000", "load_count": "1", "load_order_group": "130", "driver_type": "System Driver", "description": "UCM-UCSI KMDF Class Extension", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\System32\\Drivers\\UcmUcsiCx.sys", "file_attributes": "A", "digital_signature": "UcmUcsiCx0101", "certificate_issuer": "UCM-UCSI KMDF Class Extension", "certificate_subject": ""}, {"filename": "UcmCx.sys", "base_address": "FFFFF800`AC8D0000", "end_address": "FFFFF800`AC905000", "size": "0x00035000", "load_count": "1", "load_order_group": "131", "driver_type": "System Driver", "description": "USB Connector Manager KMDF Class Extension", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\System32\\Drivers\\UcmCx.sys", "file_attributes": "A", "digital_signature": "UcmCx0101", "certificate_issuer": "USB Connector Manager KMDF Class Extension", "certificate_subject": ""}, {"filename": "nvpcf.sys", "base_address": "FFFFF800`AC910000", "end_address": "FFFFF800`AC952000", "size": "0x00042000", "load_count": "1", "load_order_group": "132", "driver_type": "Dynamic Link Library", "description": "NVIDIA Platform Controllers and Framework", "version": "32.0.15.7582", "company": "NVIDIA Corporation", "product_name": "NVPCF", "file_created_time": "2025/6/25 10:36:53", "file_modified_time": "2025/7/12 23:48:39", "full_path": "C:\\Windows\\System32\\drivers\\nvpcf.sys", "file_attributes": "A", "digital_signature": "nvpcf", "certificate_issuer": "@oem83.inf,%nvpcf.SVCDESC%;NVPCF Service", "certificate_subject": ""}, {"filename": "UEFI.sys", "base_address": "FFFFF800`AC960000", "end_address": "FFFFF800`AC971000", "size": "0x00011000", "load_count": "1", "load_order_group": "133", "driver_type": "System Driver", "description": "用于 NT 的 ACPI 驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:03", "file_modified_time": "2022/5/7 13:19:03", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\uefi.inf_amd64_3abb917fc03c6fa8\\UEFI.sys", "file_attributes": "A", "digital_signature": "UEFI", "certificate_issuer": "@uefi.inf,%UEFI.SvcDesc%;Microsoft UEFI Driver", "certificate_subject": ""}, {"filename": "ksthunk.sys", "base_address": "FFFFF800`AC980000", "end_address": "FFFFF800`AC992000", "size": "0x00012000", "load_count": "1", "load_order_group": "135", "driver_type": "Driver", "description": "Kernel Streaming WOW Thunk Service", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:51", "file_modified_time": "2025/4/25 22:24:51", "full_path": "C:\\Windows\\system32\\drivers\\ksthunk.sys", "file_attributes": "A", "digital_signature": "ksthunk", "certificate_issuer": "Kernel Streaming Thunks", "certificate_subject": ""}, {"filename": "swenum.sys", "base_address": "FFFFF800`AC9A0000", "end_address": "FFFFF800`AC9AC000", "size": "0x0000c000", "load_count": "1", "load_order_group": "136", "driver_type": "Driver", "description": "Plug and Play Software Device Enumerator", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:02", "file_modified_time": "2022/5/7 13:19:02", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\swenum.inf_amd64_d84a235075a8ff73\\swenum.sys", "file_attributes": "A", "digital_signature": "swenum", "certificate_issuer": "@swenum.inf,%SWENUM.SVCDESC%;Software Bus Driver", "certificate_subject": ""}, {"filename": "buttonconverter.sys", "base_address": "FFFFF800`AC9B0000", "end_address": "FFFFF800`AC9C6000", "size": "0x00016000", "load_count": "1", "load_order_group": "137", "driver_type": "Dynamic Link Library", "description": "<PERSON>ton Converter Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:25", "file_modified_time": "2022/5/7 13:19:25", "full_path": "C:\\Windows\\System32\\drivers\\buttonconverter.sys", "file_attributes": "A", "digital_signature": "buttonconverter", "certificate_issuer": "@buttonconverter.inf,%btnconv.SvcDesc%;Service for Portable Device Control devices", "certificate_subject": ""}, {"filename": "UsbHub3.sys", "base_address": "FFFFF800`AC9D0000", "end_address": "FFFFF800`ACA80000", "size": "0x000b0000", "load_count": "1", "load_order_group": "138", "driver_type": "System Driver", "description": "USB3 集线器驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\UsbHub3.sys", "file_attributes": "A", "digital_signature": "USBHUB3", "certificate_issuer": "@usbhub3.inf,%UsbHub3.SVCDESC%;SuperSpeed Hub", "certificate_subject": ""}, {"filename": "USBD.SYS", "base_address": "FFFFF800`ACA90000", "end_address": "FFFFF800`ACA9F000", "size": "0x0000f000", "load_count": "6", "load_order_group": "139", "driver_type": "Dynamic Link Library", "description": "Universal Serial Bus Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\USBD.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "hidi2c.sys", "base_address": "FFFFF800`ACAA0000", "end_address": "FFFFF800`ACAB9000", "size": "0x00019000", "load_count": "1", "load_order_group": "140", "driver_type": "Dynamic Link Library", "description": "I2C HID Miniport Driver", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\hidi2c.sys", "file_attributes": "A", "digital_signature": "hidi2c", "certificate_issuer": "@hidi2c.inf,%hidi2c.SVCDESC%;Microsoft I2C HID Miniport Driver", "certificate_subject": ""}, {"filename": "nvhda64v.sys", "base_address": "FFFFF800`ACAC0000", "end_address": "FFFFF800`ACADF000", "size": "0x0001f000", "load_count": "1", "load_order_group": "141", "driver_type": "Sound Driver", "description": "NVIDIA HDMI Audio Driver", "version": "*******", "company": "NVIDIA Corporation", "product_name": "NVIDIA HDMI Audio Driver", "file_created_time": "2025/6/25 10:36:53", "file_modified_time": "2025/7/12 23:48:39", "full_path": "C:\\Windows\\system32\\drivers\\nvhda64v.sys", "file_attributes": "A", "digital_signature": "NVHDA", "certificate_issuer": "@oem81.inf,%NVHDA.SvcDesc%;Service for NVIDIA High Definition Audio Driver", "certificate_subject": ""}, {"filename": "IntcOED.sys", "base_address": "FFFFF800`ACAE0000", "end_address": "FFFFF800`ACC17000", "size": "0x00137000", "load_count": "1", "load_order_group": "142", "driver_type": "Sound Driver", "description": "Intel(R) Smart Sound Technology OED", "version": "10.29.0.9467", "company": "Intel(R) Corporation", "product_name": "Intel(R) Smart Sound Technology OED", "file_created_time": "2023/7/25 11:41:24", "file_modified_time": "2025/7/12 23:48:14", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\intcoed.inf_amd64_33284f5d2f7b1562\\IntcOED.sys", "file_attributes": "", "digital_signature": "IntcOED", "certificate_issuer": "@oem70.inf,%IntcOED.SVCDESC%;英特尔? 智音技术 OED", "certificate_subject": ""}, {"filename": "nvlddmkm.sys", "base_address": "FFFFF800`ACC20000", "end_address": "FFFFF800`********", "size": "0x06c71000", "load_count": "1", "load_order_group": "97", "driver_type": "Display Driver", "description": "NVIDIA Windows Kernel Mode Driver, Version 576.88", "version": "32.0.15.7688", "company": "NVIDIA Corporation", "product_name": "NVIDIA Windows Kernel Mode Driver, Version 576.88", "file_created_time": "2025/6/25 20:13:00", "file_modified_time": "2025/7/12 23:48:23", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\nvhmi.inf_amd64_6dfe437fad8396ef\\nvlddmkm.sys", "file_attributes": "A", "digital_signature": "nvlddmkm", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "HDAudBus.sys", "base_address": "FFFFF800`B38A0000", "end_address": "FFFFF800`B38CF000", "size": "0x0002f000", "load_count": "1", "load_order_group": "98", "driver_type": "Sound Driver", "description": "High Definition Audio Bus Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:21", "file_modified_time": "2025/4/25 22:24:21", "full_path": "C:\\Windows\\System32\\drivers\\HDAudBus.sys", "file_attributes": "A", "digital_signature": "HDAudBus", "certificate_issuer": "@hdaudbus.inf,%HDAudBus.SVCDESC%;Microsoft UAA Bus Driver for High Definition Audio", "certificate_subject": ""}, {"filename": "portcls.sys", "base_address": "FFFFF800`B38D0000", "end_address": "FFFFF800`********", "size": "0x00074000", "load_count": "7", "load_order_group": "99", "driver_type": "Sound Driver", "description": "Port Class (Class Driver for Port/Miniport Devices)", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\portcls.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "drmk.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00023000", "load_count": "1", "load_order_group": "100", "driver_type": "Dynamic Link Library", "description": "Microsoft Trusted Audio Drivers", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:21", "file_modified_time": "2025/4/25 22:24:21", "full_path": "C:\\Windows\\System32\\drivers\\drmk.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "mouhid.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00011000", "load_count": "1", "load_order_group": "143", "driver_type": "Dynamic Link Library", "description": "HID 鼠标筛选器驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:25", "file_modified_time": "2022/5/7 13:19:25", "full_path": "C:\\Windows\\System32\\drivers\\mouhid.sys", "file_attributes": "A", "digital_signature": "mou<PERSON>d", "certificate_issuer": "@msmouse.inf,%MOUHID.SvcDesc%;Mouse HID Driver", "certificate_subject": ""}, {"filename": "mouclass.sys", "base_address": "FFFFF800`B39A0000", "end_address": "FFFFF800`B39B5000", "size": "0x00015000", "load_count": "1", "load_order_group": "144", "driver_type": "System Driver", "description": "鼠标类驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:25", "file_modified_time": "2022/5/7 13:19:25", "full_path": "C:\\Windows\\System32\\drivers\\mouclass.sys", "file_attributes": "A", "digital_signature": "mouclass", "certificate_issuer": "@msmouse.inf,%mouclass.SvcDesc%;Mouse Class Driver", "certificate_subject": ""}, {"filename": "MTConfig.sys", "base_address": "FFFFF800`B39C0000", "end_address": "FFFFF800`B39CD000", "size": "0x0000d000", "load_count": "1", "load_order_group": "145", "driver_type": "System Driver", "description": "Microsoft 多点触控 HID 驱动程序", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:03", "file_modified_time": "2022/5/7 13:19:03", "full_path": "C:\\Windows\\System32\\drivers\\MTConfig.sys", "file_attributes": "A", "digital_signature": "MTConfig", "certificate_issuer": "@mtconfig.inf,%MTConfig.SVCDESC%;Microsoft Input Configuration Driver", "certificate_subject": ""}, {"filename": "kbdhid.sys", "base_address": "FFFFF800`B39D0000", "end_address": "FFFFF800`B39E3000", "size": "0x00013000", "load_count": "1", "load_order_group": "146", "driver_type": "Dynamic Link Library", "description": "HID 键盘筛选器驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\kbdhid.sys", "file_attributes": "A", "digital_signature": "kbdhid", "certificate_issuer": "@keyboard.inf,%KBDHID.SvcDesc%;Keyboard HID Driver", "certificate_subject": ""}, {"filename": "HIDCLASS.SYS", "base_address": "FFFFF800`D0A00000", "end_address": "FFFFF800`D0A44000", "size": "0x00044000", "load_count": "2", "load_order_group": "124", "driver_type": "Dynamic Link Library", "description": "Hid 类库", "version": "10.0.22621.4111", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\HIDCLASS.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "nvvad64v.sys", "base_address": "FFFFF800`D0A50000", "end_address": "FFFFF800`D0A5F000", "size": "0x0000f000", "load_count": "1", "load_order_group": "134", "driver_type": "Sound Driver", "description": "NVIDIA Virtual Audio Driver", "version": "********", "company": "NVIDIA Corporation", "product_name": "NVIDIA Virtual Audio Driver", "file_created_time": "2025/6/13 19:18:03", "file_modified_time": "2025/7/12 23:48:39", "full_path": "C:\\Windows\\system32\\drivers\\nvvad64v.sys", "file_attributes": "A", "digital_signature": "nvvad_WaveExtensible", "certificate_issuer": "@oem82.inf,%nvvad_WaveExtensible.SvcDesc%;NVIDIA Virtual Audio Device (Wave Extensible) (WDM)", "certificate_subject": ""}, {"filename": "gna.sys", "base_address": "FFFFF800`D0A60000", "end_address": "FFFFF800`D0A76000", "size": "0x00016000", "load_count": "1", "load_order_group": "103", "driver_type": "Unknown", "description": "Intel (R) GNA device driver (10.64.2.19041)", "version": "3.5.0.1578", "company": "Intel Corporation", "product_name": "Intel (R) Gaussian & Neural Accelerator", "file_created_time": "2024/1/25 22:08:07", "file_modified_time": "2025/7/12 23:48:09", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\gna.inf_amd64_8e2f374849f1eba9\\gna.sys", "file_attributes": "", "digital_signature": "IntelGNA", "certificate_issuer": "@oem67.inf,%IntelGNA.SVCDESC%;Intel(R) GNA Scoring Accelerator service", "certificate_subject": ""}, {"filename": "USBXHCI.SYS", "base_address": "FFFFF800`D0A80000", "end_address": "FFFFF800`D0B23000", "size": "0x000a3000", "load_count": "1", "load_order_group": "104", "driver_type": "System Driver", "description": "USB XHCI 驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\USBXHCI.SYS", "file_attributes": "A", "digital_signature": "USBXHCI", "certificate_issuer": "@usbxhci.inf,%PCI\\CC_0C0330.DeviceDesc%;USB xHCI Compliant Host Controller", "certificate_subject": ""}, {"filename": "ucx01000.sys", "base_address": "FFFFF800`D0B30000", "end_address": "FFFFF800`D0B77000", "size": "0x00047000", "load_count": "1", "load_order_group": "105", "driver_type": "System Driver", "description": "USB Controller Extension", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\system32\\drivers\\ucx01000.sys", "file_attributes": "A", "digital_signature": "Ucx01000", "certificate_issuer": "USB Host Support Library", "certificate_subject": ""}, {"filename": "Netwtw14.sys", "base_address": "FFFFF800`D0B80000", "end_address": "FFFFF800`********", "size": "0x005d4000", "load_count": "1", "load_order_group": "106", "driver_type": "Network Driver", "description": "R Intel Wireless WiFi Link Driver", "version": "*********", "company": "Intel Corporation", "product_name": "Intel? Wireless WiFi Link Adapter", "file_created_time": "2025/5/4 15:53:10", "file_modified_time": "2025/7/12 23:48:03", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\netwtw6e.inf_amd64_d71df66cae535bcb\\Netwtw14.sys", "file_attributes": "", "digital_signature": "Netwtw14", "certificate_issuer": "___ Intel(R) Wireless 适配器驱动程序（适用于 Windows 10 64 位）", "certificate_subject": ""}, {"filename": "wdiwifi.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00107000", "load_count": "1", "load_order_group": "107", "driver_type": "System Driver", "description": "WDI Driver Framework Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:26", "file_modified_time": "2025/4/25 22:24:26", "full_path": "C:\\Windows\\system32\\DRIVERS\\wdiwifi.sys", "file_attributes": "A", "digital_signature": "wdiwifi", "certificate_issuer": "WDI Driver Framework", "certificate_subject": ""}, {"filename": "vwifibus.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00010000", "load_count": "1", "load_order_group": "108", "driver_type": "System Driver", "description": "Virtual Wireless Bus Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:11", "file_modified_time": "2022/5/7 13:19:11", "full_path": "C:\\Windows\\System32\\drivers\\vwifibus.sys", "file_attributes": "A", "digital_signature": "vwifibus", "certificate_issuer": "Virtual Wireless Bus Driver", "certificate_subject": ""}, {"filename": "iaLPSS2_I2C_ADL.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`D12C4000", "size": "0x00034000", "load_count": "1", "load_order_group": "109", "driver_type": "System Driver", "description": "Intel(R) Serial IO I2C Driver v2", "version": "30.100.2221.20", "company": "Intel Corporation", "product_name": "Intel(R) Serial IO Driver", "file_created_time": "2022/6/23 0:45:44", "file_modified_time": "2025/7/12 23:48:02", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\ialpss2_i2c_adl.inf_amd64_1ebed6f33a1c1014\\iaLPSS2_I2C_ADL.sys", "file_attributes": "", "digital_signature": "iaLPSS2_I2C_ADL", "certificate_issuer": "@oem60.inf,%iaLPSS2_I2C_ADL.SVCDESC%;Intel(R) Serial IO I2C Driver v2", "certificate_subject": ""}, {"filename": "SpbCx.sys", "base_address": "FFFFF800`D12D0000", "end_address": "FFFFF800`D12EC000", "size": "0x0001c000", "load_count": "1", "load_order_group": "110", "driver_type": "Dynamic Link Library", "description": "SPB Class Extension", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\system32\\drivers\\SpbCx.sys", "file_attributes": "A", "digital_signature": "SpbCx", "certificate_issuer": "Simple Peripheral Bus Support Library", "certificate_subject": ""}, {"filename": "TeeDriverW10x64.sys", "base_address": "FFFFF800`D12F0000", "end_address": "FFFFF800`********", "size": "0x00050000", "load_count": "1", "load_order_group": "111", "driver_type": "System Driver", "description": "Intel(R) Management Engine Interface", "version": "2452.7.1.0", "company": "Intel Corporation", "product_name": "Intel(R) Management Engine Interface", "file_created_time": "2024/12/29 8:40:52", "file_modified_time": "2025/7/12 23:48:02", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\heci.inf_amd64_345c05c2cb146748\\x64\\TeeDriverW10x64.sys", "file_attributes": "", "digital_signature": "MEIx64", "certificate_issuer": "@oem63.inf,%TEE_SvcDesc%;Intel(R) Management Engine Interface", "certificate_subject": ""}, {"filename": "i8042prt.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00027000", "load_count": "1", "load_order_group": "113", "driver_type": "System Driver", "description": "i8042 端口驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\i8042prt.sys", "file_attributes": "A", "digital_signature": "i8042prt", "certificate_issuer": "@keyboard.inf,%i8042prt.SvcDesc%;i8042 Keyboard and PS/2 Mouse Port Driver", "certificate_subject": ""}, {"filename": "kbdclass.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00015000", "load_count": "1", "load_order_group": "114", "driver_type": "System Driver", "description": "键盘类驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\System32\\drivers\\kbdclass.sys", "file_attributes": "A", "digital_signature": "kbdclass", "certificate_issuer": "@keyboard.inf,%kbdclass.SvcDesc%;Keyboard Class Driver", "certificate_subject": ""}, {"filename": "IntcAudioBus.sys", "base_address": "FFFFF800`D13A0000", "end_address": "FFFFF800`D13F0000", "size": "0x00050000", "load_count": "1", "load_order_group": "115", "driver_type": "Sound Driver", "description": "Intel(R) Smart Sound Technology BUS", "version": "10.29.0.9467", "company": "Intel(R) Corporation", "product_name": "Intel(R) Smart Sound Technology BUS", "file_created_time": "2023/7/25 11:41:16", "file_modified_time": "2025/7/12 23:48:13", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\intcaudiobus.inf_amd64_cb0445dfe5544239\\IntcAudioBus.sys", "file_attributes": "", "digital_signature": "IntcAudioBus", "certificate_issuer": "@oem69.inf,%IntcAudioBus.SVCDESC%;英特尔? 智音技术总线", "certificate_subject": ""}, {"filename": "acpitime.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`D140E000", "size": "0x0000e000", "load_count": "1", "load_order_group": "116", "driver_type": "System Driver", "description": "ACPI Wake Alarm", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:04", "file_modified_time": "2022/5/7 13:19:04", "full_path": "C:\\Windows\\System32\\drivers\\acpitime.sys", "file_attributes": "A", "digital_signature": "acpitime", "certificate_issuer": "@acpitime.inf,%AcpiTime.SvcDesc%;ACPI Wake Alarm Driver", "certificate_subject": ""}, {"filename": "iaLPSS2_GPIO2_ADL.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00022000", "load_count": "1", "load_order_group": "117", "driver_type": "System Driver", "description": "Intel(R) Serial IO GPIO Driver v2", "version": "30.100.2221.20", "company": "Intel Corporation", "product_name": "Intel(R) Serial IO Driver", "file_created_time": "2022/6/23 0:45:40", "file_modified_time": "2025/7/12 23:48:02", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\ialpss2_gpio2_adl.inf_amd64_774a66f35d00ad3d\\iaLPSS2_GPIO2_ADL.sys", "file_attributes": "", "digital_signature": "iaLPSS2_GPIO2_ADL", "certificate_issuer": "@oem61.inf,%iaLPSS2_GPIO2_ADL.SVCDESC%;Intel(R) Serial IO GPIO Driver v2", "certificate_subject": ""}, {"filename": "msgpioclx.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`********", "size": "0x00035000", "load_count": "1", "load_order_group": "118", "driver_type": "Dynamic Link Library", "description": "GPIO Class Extension Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:31", "file_modified_time": "2025/4/25 22:24:31", "full_path": "C:\\Windows\\System32\\Drivers\\msgpioclx.sys", "file_attributes": "A", "digital_signature": "GPIOClx0101", "certificate_issuer": "Microsoft GPIO Class Extension Driver", "certificate_subject": ""}, {"filename": "igdkmdn64.sys", "base_address": "FFFFF800`********", "end_address": "FFFFF800`D4F50000", "size": "0x03ad0000", "load_count": "1", "load_order_group": "102", "driver_type": "Display Driver", "description": "Intel Graphics Kernel Mode New Driver", "version": "32.0.101.6877", "company": "Intel Corporation", "product_name": "Intel HD Graphics Drivers for Windows(R)", "file_created_time": "2025/6/4 21:33:54", "file_modified_time": "2025/7/12 23:48:25", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\iigd_dch.inf_amd64_a18415a32b0a7015\\igdkmdn64.sys", "file_attributes": "", "digital_signature": "igfxn", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "intelppm.sys", "base_address": "FFFFF800`D4F60000", "end_address": "FFFFF800`D4FAF000", "size": "0x0004f000", "load_count": "1", "load_order_group": "119", "driver_type": "System Driver", "description": "Processor Device Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\intelppm.sys", "file_attributes": "A", "digital_signature": "intelppm", "certificate_issuer": "@cpu.inf,%IntelPPM.SvcDesc%;Intel Processor Driver", "certificate_subject": ""}, {"filename": "acpipagr.sys", "base_address": "FFFFF800`D4FB0000", "end_address": "FFFFF800`D4FBC000", "size": "0x0000c000", "load_count": "1", "load_order_group": "120", "driver_type": "System Driver", "description": "ACPI Processor Aggregator Device Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:04", "file_modified_time": "2022/5/7 13:19:04", "full_path": "C:\\Windows\\System32\\drivers\\acpipagr.sys", "file_attributes": "A", "digital_signature": "acpipagr", "certificate_issuer": "@acpipagr.inf,%SvcDesc%;ACPI Processor Aggregator Driver", "certificate_subject": ""}, {"filename": "wmiacpi.sys", "base_address": "FFFFF800`D4FC0000", "end_address": "FFFFF800`D4FCE000", "size": "0x0000e000", "load_count": "1", "load_order_group": "121", "driver_type": "System Driver", "description": "Windows Management Interface for ACPI", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:23", "file_modified_time": "2025/4/25 22:24:23", "full_path": "C:\\Windows\\System32\\drivers\\wmiacpi.sys", "file_attributes": "A", "digital_signature": "WmiAcpi", "certificate_issuer": "@wmiacpi.inf,%WMIMAP.SvcDesc%;Microsoft Windows Management Interface for ACPI", "certificate_subject": ""}, {"filename": "HidEventFilter.sys", "base_address": "FFFFF800`D4FD0000", "end_address": "FFFFF800`D4FE7000", "size": "0x00017000", "load_count": "1", "load_order_group": "122", "driver_type": "Driver", "description": "Intel(R) HID Event Filter", "version": "********", "company": "Intel Corporation", "product_name": "Intel(R) HID Event Filter", "file_created_time": "2024/4/30 4:38:24", "file_modified_time": "2025/7/12 23:48:09", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\hideventfilter.inf_amd64_a68fcfefc5a69554\\HidEventFilter.sys", "file_attributes": "", "digital_signature": "Hid<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificate_issuer": "@oem66.inf,%HidEventFilter%;Intel(R) HID Event Filter", "certificate_subject": ""}, {"filename": "mshidkmdf.sys", "base_address": "FFFFF800`D4FF0000", "end_address": "FFFFF800`D4FFC000", "size": "0x0000c000", "load_count": "1", "load_order_group": "123", "driver_type": "System Driver", "description": "Pass-through HID to KMDF Filter Driver", "version": "10.0.22621.1", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2022/5/7 13:19:23", "file_modified_time": "2022/5/7 13:19:23", "full_path": "C:\\Windows\\System32\\drivers\\mshidkmdf.sys", "file_attributes": "A", "digital_signature": "mshidkmdf", "certificate_issuer": "@mshidkmdf.inf,%mshidkmdf.SvcName%;Pass-through HID to KMDF Filter Driver", "certificate_subject": ""}, {"filename": "BTHUSB.sys", "base_address": "FFFFF800`DC800000", "end_address": "FFFFF800`DC822000", "size": "0x00022000", "load_count": "1", "load_order_group": "151", "driver_type": "Dynamic Link Library", "description": "蓝牙微型端口驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\System32\\drivers\\BTHUSB.sys", "file_attributes": "A", "digital_signature": "BTHUSB", "certificate_issuer": "@bth.inf,%BTHUSB.SvcDesc%;蓝牙无线电收发器 USB 驱动程序", "certificate_subject": ""}, {"filename": "BTHport.sys", "base_address": "FFFFF800`DC830000", "end_address": "FFFFF800`DCA32000", "size": "0x00202000", "load_count": "1", "load_order_group": "152", "driver_type": "Dynamic Link Library", "description": "蓝牙总线驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\System32\\drivers\\BTHport.sys", "file_attributes": "A", "digital_signature": "BTHPORT", "certificate_issuer": "@bth.inf,%BTHPORT.SvcDesc%;蓝牙端口驱动程序", "certificate_subject": ""}, {"filename": "hidusb.sys", "base_address": "FFFFF800`DCA40000", "end_address": "FFFFF800`DCA53000", "size": "0x00013000", "load_count": "1", "load_order_group": "153", "driver_type": "Dynamic Link Library", "description": "USB Miniport Driver for Input Devices", "version": "10.0.22621.4111", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\System32\\drivers\\hidusb.sys", "file_attributes": "A", "digital_signature": "HidUsb", "certificate_issuer": "@input.inf,%HID.SvcDesc%;Microsoft HID Class Driver", "certificate_subject": ""}, {"filename": "usbvideo.sys", "base_address": "FFFFF800`DCAC0000", "end_address": "FFFFF800`DCB1E000", "size": "0x0005e000", "load_count": "1", "load_order_group": "154", "driver_type": "Driver", "description": "USB Video Class Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\Drivers\\usbvideo.sys", "file_attributes": "A", "digital_signature": "usbvideo", "certificate_issuer": "@usbvideo.inf,%USBVideo.SvcDesc%;USB Video Device (WDM)", "certificate_subject": ""}, {"filename": "bthpan.sys", "base_address": "FFFFF800`DCB20000", "end_address": "FFFFF800`DCB47000", "size": "0x00027000", "load_count": "1", "load_order_group": "155", "driver_type": "System Driver", "description": "Bluetooth Personal Area Networking", "version": "10.0.22621.2506", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\System32\\drivers\\bthpan.sys", "file_attributes": "A", "digital_signature": "BthPan", "certificate_issuer": "@bthpan.inf,%BthPan.DisplayName%;Bluetooth Device (Personal Area Network)", "certificate_subject": ""}, {"filename": "WIN32KSGD.SYS", "base_address": "FFFFF800`DCB50000", "end_address": "FFFFF800`DCB5C000", "size": "0x0000c000", "load_count": "6", "load_order_group": "157", "driver_type": "Dynamic Link Library", "description": "Win32k temporary session global driver", "version": "10.0.22621.3810", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:37", "file_modified_time": "2025/4/25 22:24:37", "full_path": "C:\\Windows\\System32\\WIN32KSGD.SYS", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "dump_dumpstorport.sys", "base_address": "FFFFF800`DCB80000", "end_address": "FFFFF800`DCB91000", "size": "0x00011000", "load_count": "2", "load_order_group": "158", "driver_type": "Unknown", "description": "", "version": "", "company": "", "product_name": "", "file_created_time": "N/A", "file_modified_time": "N/A", "full_path": "C:\\Windows\\System32\\Drivers\\dump_dumpstorport.sys", "file_attributes": "", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "dump_stornvme.sys", "base_address": "FFFFF800`DCBE0000", "end_address": "FFFFF800`DCC1A000", "size": "0x0003a000", "load_count": "1", "load_order_group": "159", "driver_type": "Unknown", "description": "", "version": "", "company": "", "product_name": "", "file_created_time": "N/A", "file_modified_time": "N/A", "full_path": "C:\\Windows\\System32\\drivers\\dump_stornvme.sys", "file_attributes": "", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "dump_dumpfve.sys", "base_address": "FFFFF800`DCC40000", "end_address": "FFFFF800`DCC5F000", "size": "0x0001f000", "load_count": "1", "load_order_group": "160", "driver_type": "Unknown", "description": "", "version": "", "company": "", "product_name": "", "file_created_time": "N/A", "file_modified_time": "N/A", "full_path": "C:\\Windows\\System32\\Drivers\\dump_dumpfve.sys", "file_attributes": "", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "dxgmms2.sys", "base_address": "FFFFF800`DCC60000", "end_address": "FFFFF800`DCD77000", "size": "0x00117000", "load_count": "1", "load_order_group": "163", "driver_type": "System Driver", "description": "DirectX Graphics MMS", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:31", "file_modified_time": "2025/4/25 22:24:31", "full_path": "C:\\Windows\\System32\\drivers\\dxgmms2.sys", "file_attributes": "A", "digital_signature": "", "certificate_issuer": "", "certificate_subject": ""}, {"filename": "monitor.sys", "base_address": "FFFFF800`DCD80000", "end_address": "FFFFF800`DCD9E000", "size": "0x0001e000", "load_count": "1", "load_order_group": "164", "driver_type": "System Driver", "description": "Monitor Driver", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:22", "file_modified_time": "2025/4/25 22:24:22", "full_path": "C:\\Windows\\System32\\drivers\\monitor.sys", "file_attributes": "A", "digital_signature": "monitor", "certificate_issuer": "@monitor.inf,%Monitor.SVCDESC%;Microsoft Monitor Class Function Driver Service", "certificate_subject": ""}, {"filename": "bfs.sys", "base_address": "FFFFF800`DCDA0000", "end_address": "FFFFF800`DCDB4000", "size": "0x00014000", "load_count": "1", "load_order_group": "167", "driver_type": "System Driver", "description": "Bfs 筛选器驱动程序", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:39", "file_modified_time": "2025/4/25 22:24:39", "full_path": "C:\\Windows\\system32\\drivers\\bfs.sys", "file_attributes": "A", "digital_signature": "bfs", "certificate_issuer": "中转文件系统", "certificate_subject": ""}, {"filename": "storqosflt.sys", "base_address": "FFFFF800`DCDC0000", "end_address": "FFFFF800`DCDDB000", "size": "0x0001b000", "load_count": "1", "load_order_group": "170", "driver_type": "System Driver", "description": "存储 QoS 筛选器", "version": "10.0.22621.5262", "company": "Microsoft Corporation", "product_name": "Microsoft? Windows? Operating System", "file_created_time": "2025/4/25 22:24:38", "file_modified_time": "2025/4/25 22:24:38", "full_path": "C:\\Windows\\system32\\drivers\\storqosflt.sys", "file_attributes": "A", "digital_signature": "storqosflt", "certificate_issuer": "Storage QoS Filter Driver", "certificate_subject": ""}, {"filename": "ibtusb.sys", "base_address": "FFFFF800`DCDF0000", "end_address": "FFFFF800`DDFF2000", "size": "0x01202000", "load_count": "1", "load_order_group": "150", "driver_type": "Unknown", "description": "Intel(R) Wireless Bluetooth(R) Filter Driver", "version": "**********", "company": "Intel Corporation", "product_name": "Intel(R) Wireless Bluetooth(R)", "file_created_time": "2025/5/4 1:34:18", "file_modified_time": "2025/7/12 23:48:09", "full_path": "C:\\Windows\\System32\\DriverStore\\FileRepository\\ibtusb.inf_amd64_464f66b674cab9f9\\ibtusb.sys", "file_attributes": "", "digital_signature": "ibtusb", "certificate_issuer": "@oem68.inf,%ibtusb.SVCDESC_IBT%;英特尔(R) 无线 Bluetooth(R)", "certificate_subject": ""}]