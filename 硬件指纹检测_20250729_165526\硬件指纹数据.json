[{"device_name": "Base System Device", "device_id": "", "hardware_id": "", "compatible_id": "", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_0740&SUBSYS_074015AD&REV_10\\3&61aaa01&0&3F", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,7,7)", "problem_status": "0x00000000", "disabled": "0x00000040", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:10", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "", "device_description": "2025/5/9 13:48:10", "device_type": "2025/5/9 13:48:10", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "", "driver_date": "", "driver_company": "", "inf_file": "", "inf_section": "", "last_arrival_time": "2025/7/29 15:53:10"}, {"device_name": "Intel High Definition Audio", "device_id": "", "hardware_id": "", "compatible_id": "", "instance_id": "", "service_name": "INTELAUDIO\\FUNC_01&VEN_10EC&DEV_0245&SUBSYS_103C8BB2&REV_1000\\5&2d03116d&0&0001", "problem_code": "HD Audio Bus Driver", "problem_status": "0x00000000", "disabled": "0x00000040", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:26", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "", "device_description": "2025/7/12 23:48:26", "device_type": "2025/7/12 23:48:26", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "", "driver_date": "", "driver_company": "", "inf_file": "", "inf_section": "", "last_arrival_time": "2025/7/29 15:53:10"}, {"device_name": "Generic USB Hub", "device_id": "(Generic USB Hub)", "hardware_id": "<PERSON><PERSON><PERSON>b", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_0E0F&PID_0002\\6&35d1f50b&0&2", "problem_code": "Port_#0002.Hub_#0001", "problem_status": "0x00000084", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:09", "first_install_time": "2025/5/9 13:49:07", "firmware_revision": "", "device_class_guid": "{8d9256f1-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:09", "device_type": "2025/5/9 13:48:09", "device_location": "2025/5/10 12:52:10", "driver_description": "", "driver_version": "Generic USB Hub", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "StandardHub.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:09"}, {"device_name": "NECVMWar VMware SATA CD01", "device_id": "(Standard CD-ROM drives)", "hardware_id": "cdrom", "compatible_id": "CDROM", "instance_id": "", "service_name": "SCSI\\CdRom&Ven_NECVMWar&Prod_VMware_SATA_CD01\\5&260e6d66&0&010000", "problem_code": "Bus Number 1, Target Id 0, LUN 0", "problem_status": "0x00000064", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:06", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e965-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/10 12:59:39", "device_type": "2025/5/9 13:48:06", "device_location": "2025/5/10 12:59:39", "driver_description": "", "driver_version": "CD-ROM Drive", "driver_date": "10.0.22621.1", "driver_company": "cdrom.inf", "inf_file": "cdrom_install", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:06"}, {"device_name": "ACPI x64-based PC", "device_id": "(Standard computers)", "hardware_id": "\\Driver\\ACPI_HAL", "compatible_id": "Computer", "instance_id": "", "service_name": "ROOT\\ACPI_HAL\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/29 4:35:41", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e966-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "ACPI x64-based PC", "driver_date": "10.0.22621.1", "driver_company": "hal.inf", "inf_file": "ACPI_AMD64_HAL", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "SAMSUNG MZVL2512HCJQ-00BH1", "device_id": "(Standard disk drives)", "hardware_id": "disk", "compatible_id": "DiskDrive", "instance_id": "", "service_name": "SCSI\\Disk&Ven_NVMe&Prod_SAMSUNG_MZVL2512\\5&bb0af3a&0&000000", "problem_code": "Bus Number 0, Target Id 0, LUN 0", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:58:00", "firmware_revision": "D:\\", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e967-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:58:00", "device_type": "2025/7/12 23:58:00", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Disk drive", "driver_date": "10.0.22621.5262", "driver_company": "disk.inf", "inf_file": "disk_install.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:58:00"}, {"device_name": "Microsoft Basic Display Driver", "device_id": "(Standard display types)", "hardware_id": "BasicDisplay", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\BasicDisplay\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:11", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "", "driver_description": "", "driver_version": "Microsoft Basic Display Driver", "driver_date": "10.0.22621.1", "driver_company": "basicdisplay.inf", "inf_file": "MSBDD_Fallback", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "Microsoft Basic Display Adapter", "device_id": "(Standard display types)", "hardware_id": "BasicDisplay", "compatible_id": "Display", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_0405&SUBSYS_040515AD&REV_00\\3&61aaa01&0&78", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,15,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:11", "first_install_time": "2025/5/9 13:48:10", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e968-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:10", "device_type": "2025/5/9 13:48:10", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Microsoft Basic Display Adapter", "driver_date": "10.0.22621.1", "driver_company": "display.inf", "inf_file": "MSBDA", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/10 12:52:10"}, {"device_name": "ATA Channel 0", "device_id": "(Standard IDE ATA/ATAPI controllers)", "hardware_id": "atapi", "compatible_id": "HDC", "instance_id": "", "service_name": "PCIIDE\\IDEChannel\\4&23686003&0&0", "problem_code": "Channel 0", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:04", "first_install_time": "2025/5/10 12:52:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96a-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "IDE Channel", "driver_date": "10.0.22621.2506", "driver_company": "mshdc.inf", "inf_file": "atapi_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "ATA Channel 1", "device_id": "(Standard IDE ATA/ATAPI controllers)", "hardware_id": "atapi", "compatible_id": "HDC", "instance_id": "", "service_name": "PCIIDE\\IDEChannel\\4&23686003&0&1", "problem_code": "Channel 1", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:04", "first_install_time": "2025/5/10 12:52:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96a-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "IDE Channel", "driver_date": "10.0.22621.2506", "driver_company": "mshdc.inf", "inf_file": "atapi_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "HID Keyboard Device", "device_id": "(Standard keyboards)", "hardware_id": "kbdhid", "compatible_id": "Keyboard", "instance_id": "", "service_name": "HID\\ConvertedDevice&Col01\\5&1feb78c6&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:12", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:48:12", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "HID Keyboard Device", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "HID_Keyboard_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "HID Keyboard Device", "device_id": "(Standard keyboards)", "hardware_id": "kbdhid", "compatible_id": "Keyboard", "instance_id": "", "service_name": "HID\\VID_1CA2&PID_0405&MI_00\\8&1a5886&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID Keyboard Device", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "HID_Keyboard_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "HID Keyboard Device", "device_id": "(Standard keyboards)", "hardware_id": "kbdhid", "compatible_id": "Keyboard", "instance_id": "", "service_name": "HID\\VID_1CA2&PID_0405&MI_01&Col01\\8&17dde4cb&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID Keyboard Device", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "HID_Keyboard_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "HID Keyboard Device", "device_id": "(Standard keyboards)", "hardware_id": "kbdhid", "compatible_id": "Keyboard", "instance_id": "", "service_name": "HID\\VID_5253&PID_1020&MI_01&Col01\\8&18e5aef4&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID Keyboard Device", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "HID_Keyboard_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "HID Keyboard Device", "device_id": "(Standard keyboards)", "hardware_id": "kbdhid", "compatible_id": "Keyboard", "instance_id": "", "service_name": "HID\\VID_5253&PID_00A1&MI_01&Col01\\8&8a0a89f&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/19 21:56:00", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "HID Keyboard Device", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "HID_Keyboard_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "Standard PS/2 Keyboard", "device_id": "(Standard keyboards)", "hardware_id": "i8042prt", "compatible_id": "Keyboard", "instance_id": "", "service_name": "ACPI\\PNP0303\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Standard PS/2 Keyboard", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "STANDARD_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Standard PS/2 Keyboard", "device_id": "(Standard keyboards)", "hardware_id": "i8042prt", "compatible_id": "Keyboard", "instance_id": "", "service_name": "ACPI\\HPQ8001\\4&2279ca69&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Standard PS/2 Keyboard", "driver_date": "10.0.22621.5262", "driver_company": "keyboard.inf", "inf_file": "STANDARD_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "@System32\\drivers\\dxgkrnl.sys,#300;Generic Monitor", "device_id": "(Standard monitor types)", "hardware_id": "monitor", "compatible_id": "Monitor", "instance_id": "", "service_name": "DISPLAY\\Default_Monitor\\4&427137e&0&UID0", "problem_code": "", "problem_status": "0x000000e4", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:11", "first_install_time": "2025/5/9 13:48:11", "firmware_revision": "", "device_class_guid": "{298580e3-e0e9-5b21-9699-52225753ef2e}", "device_manufacturer": "{4d36e96e-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:11", "device_type": "2025/5/9 13:48:11", "device_location": "2025/5/10 12:52:10", "driver_description": "", "driver_version": "Generic Non-PnP Monitor", "driver_date": "10.0.22621.5262", "driver_company": "monitor.inf", "inf_file": "NonPnPMonitor.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:11"}, {"device_name": "@System32\\drivers\\dxgkrnl.sys,#301;Integrated Monitor", "device_id": "(Standard monitor types)", "hardware_id": "monitor", "compatible_id": "Monitor", "instance_id": "", "service_name": "DISPLAY\\CMN1521\\4&33e2c278&0&UID8388688", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:57", "first_install_time": "2025/7/12 23:58:04", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96e-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:57", "device_type": "2025/7/12 23:48:57", "device_location": "2025/7/29 4:35:47", "driver_description": "", "driver_version": "Generic PnP Monitor", "driver_date": "10.0.22621.5262", "driver_company": "monitor.inf", "inf_file": "PnPMonitor.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:57"}, {"device_name": "@System32\\drivers\\dxgkrnl.sys,#301;Integrated Monitor", "device_id": "(Standard monitor types)", "hardware_id": "monitor", "compatible_id": "Monitor", "instance_id": "", "service_name": "DISPLAY\\CMN1521\\4&33e2c278&0&UID0", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96e-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/12 23:20:22", "driver_description": "2025/7/12 23:48:56", "driver_version": "Generic PnP Monitor", "driver_date": "10.0.22621.5262", "driver_company": "monitor.inf", "inf_file": "PnPMonitor.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "@System32\\drivers\\dxgkrnl.sys,#303;Generic Monitor (%1);(24G15N)", "device_id": "(Standard monitor types)", "hardware_id": "monitor", "compatible_id": "Monitor", "instance_id": "", "service_name": "DISPLAY\\AOC2415\\5&3337fd88&0&UID4352", "problem_code": "", "problem_status": "0x000000e4", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "Yes", "install_time": "2025/7/12 23:51:21", "first_install_time": "2025/7/12 23:58:05", "firmware_revision": "", "device_class_guid": "{518c4278-4817-51e0-8c74-adb9d4177f17}", "device_manufacturer": "{4d36e96e-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:51:21", "device_type": "2025/7/12 23:51:21", "device_location": "2025/7/29 4:35:48", "driver_description": "", "driver_version": "Generic PnP Monitor", "driver_date": "10.0.22621.5262", "driver_company": "monitor.inf", "inf_file": "PnPMonitor.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:51:21"}, {"device_name": "@System32\\drivers\\dxgkrnl.sys,#300;Generic Monitor", "device_id": "(Standard monitor types)", "hardware_id": "monitor", "compatible_id": "Monitor", "instance_id": "", "service_name": "DISPLAY\\Default_Monitor\\1&8713bca&0&UID0", "problem_code": "", "problem_status": "0x000000e4", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:56", "first_install_time": "2025/7/12 23:48:56", "firmware_revision": "", "device_class_guid": "{1074b24d-b986-5a01-b420-b3d39c2f9286}", "device_manufacturer": "{4d36e96e-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:56", "device_type": "2025/7/12 23:48:56", "device_location": "2025/7/12 23:48:56", "driver_description": "2025/7/12 23:48:56", "driver_version": "Generic Non-PnP Monitor", "driver_date": "10.0.22621.5262", "driver_company": "monitor.inf", "inf_file": "NonPnPMonitor.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:56"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&BE", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,6)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&BD", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,5)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&BC", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,4)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C4", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,4)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&BB", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,3)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_5253&PID_1020&MI_02\\7&50df75c&0&0002", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\INT340E\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&BA", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,2)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B9", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,1)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\INTC109C\\4&2279ca69&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B8", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/10 12:52:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_5253&PID_1020&MI_01\\7&50df75c&0&0001", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "Programmable interrupt controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0000\\4&2279ca69&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "No", "install_time": "2025/7/12 23:07:57", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Programmable interrupt controller", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_PIC", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "EISA programmable interrupt controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0001\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "EISA programmable interrupt controller", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_PIC", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "System timer", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0100\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "System timer", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "System timer", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0100\\4&2279ca69&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "System timer", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "High precision event timer", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0103\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "No", "install_time": "2025/7/12 23:07:54", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "High precision event timer", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_HPET", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "High precision event timer", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0103\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "No", "install_time": "2025/7/12 23:07:54", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "High precision event timer", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_HPET", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Direct memory access controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0200\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "No", "install_time": "2025/7/12 23:07:54", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Direct memory access controller", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C5", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,5)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "System speaker", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0800\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "System speaker", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Bus", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A03\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/10 12:52:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Bus", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_ROOT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\10", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\11", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\12", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\13", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\14", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\15", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\16", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\17", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\18", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\19", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\1a", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\1b", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\1c", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\1d", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\1e", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\1f", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\20", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\21", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\22", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\23", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\24", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\25", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\26", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\27", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\28", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\29", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\2a", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\2b", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\2c", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\2d", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\2e", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\2f", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\30", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\31", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\32", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\33", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\34", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\35", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\36", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\37", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&BF", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,23,7)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\39", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\3a", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\3b", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\3c", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\3d", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\3e", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\3f", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\40", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\41", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C6", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,6)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\43", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\44", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\45", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\46", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\47", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\48", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\49", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4a", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4b", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4c", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4d", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4e", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\4f", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\50", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\51", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\52", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\53", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\54", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\55", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\56", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\57", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "PCI Express Root Complex", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A08\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:41", "first_install_time": "2025/7/29 4:35:41", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI Express Root Complex", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_ROOT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "System CMOS/real time clock", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0B00\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "System CMOS/real time clock", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\1", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\1f", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\2", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\4", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\5", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\IoTraps", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Motherboard resources", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C02\\ISCLK", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Motherboard resources", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_MBRES", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft ACPI-Compliant Embedded Controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C09\\1", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/29 4:35:41", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft ACPI-Compliant Embedded Controller", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_5253&PID_1020&MI_00\\7&50df75c&0&0000", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "ACPI Power Button", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C0C\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:15", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "ACPI Power Button", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "ACPI Lid", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C0D\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:15", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "ACPI Lid", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B7", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,7)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_5253&PID_00A1&MI_02\\7&184a288&0&0002", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/27 19:24:43", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_5253&PID_00A1&MI_01\\7&184a288&0&0001", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/27 19:24:43", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_5253&PID_00A1&MI_00\\7&184a288&0&0000", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/27 19:24:43", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "ACPI Thermal Zone", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\ThermalZone\\TZ01", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:15", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "ACPI Thermal Zone", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B6", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,6)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_1CA2&PID_0405&MI_02\\7&2f155648&0&0002", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_1CA2&PID_0405&MI_01\\7&2f155648&0&0001", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_1CA2&PID_0405&MI_00\\7&2f155648&0&0000", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B5", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,5)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B4", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,4)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B3", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,3)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_0E0F&PID_0003&MI_01\\7&bcbfcc2&0&0001", "problem_code": "000b.0000.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:10", "first_install_time": "2025/5/10 12:52:20", "firmware_revision": "", "device_class_guid": "{8d9256e9-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:08"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_0E0F&PID_0003&MI_00\\7&bcbfcc2&0&0000", "problem_code": "000b.0000.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:09", "first_install_time": "2025/5/10 12:52:18", "firmware_revision": "", "device_class_guid": "{8d9256e9-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/5/9 13:48:07", "device_type": "2025/5/9 13:48:07", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:07"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B2", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,2)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C7", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,7)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "USB Input Device", "device_id": "(Standard system devices)", "hardware_id": "HidUsb", "compatible_id": "HIDClass", "instance_id": "", "service_name": "USB\\VID_0D8C&PID_0343&MI_00\\6&18a8239b&0&0000", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:17", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "USB Input Device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B1", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,1)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C0", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C1", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,1)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C2", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,2)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\38", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&C3", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,24,3)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&B0", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/10 12:52:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&AF", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,7)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&AE", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,6)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI standard ISA bridge", "device_id": "(Standard system devices)", "hardware_id": "msisadrv", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_519D&SUBSYS_8BB2103C&REV_01\\3&11583659&0&F8", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,31,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI standard ISA bridge", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "MSISADRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&AD", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,5)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "HID-compliant system controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\ConvertedDevice&Col03\\5&1feb78c6&0&0002", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:48:12", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "HID-compliant system controller", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&AC", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,4)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\ELAN0788&Col02\\5&34a72ad8&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:05", "device_type": "2025/7/12 23:48:05", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:05"}, {"device_name": "HID-compliant touch pad", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\ELAN0788&Col03\\5&34a72ad8&0&0002", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:05", "device_type": "2025/7/12 23:48:05", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "HID-compliant touch pad", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:05"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&AB", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,3)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "HID-compliant wireless radio controls", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\INTC816&Col01\\3&1535a34c&0&0000", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:48:12", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "HID-compliant wireless radio controls", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&AA", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,2)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&A9", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,1)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "HID-compliant phone", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_0D8C&PID_0343&MI_00&Col02\\7&1c49d3c4&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:17", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "HID-compliant phone", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_0D8C&PID_0343&MI_00&Col03\\7&1c49d3c4&0&0002", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:17", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_0D8C&PID_0343&MI_00&Col04\\7&1c49d3c4&0&0003", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:17", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07A0&SUBSYS_07A015AD&REV_01\\3&61aaa01&0&A8", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/10 12:52:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI-to-PCI Bridge", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_0790&SUBSYS_079015AD&REV_02\\3&61aaa01&0&88", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,17,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/10 12:52:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI-to-PCI Bridge", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51BC&SUBSYS_8BB2103C&REV_01\\3&11583659&0&E0", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,28,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:41", "first_install_time": "2025/7/29 4:35:41", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51BF&SUBSYS_8BB2103C&REV_01\\3&11583659&0&E7", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,28,7)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:41", "first_install_time": "2025/7/29 4:35:41", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "PCI standard RAM Controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51EF&SUBSYS_00000000&REV_01\\3&11583659&0&A2", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,20,2)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI standard RAM Controller", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "HID-compliant system controller", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_1CA2&PID_0405&MI_01&Col03\\8&17dde4cb&0&0002", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant system controller", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "PCI-to-PCI Bridge", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_7191&SUBSYS_00000000&REV_01\\3&61aaa01&0&08", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,1,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:02", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI-to-PCI Bridge", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_1CA2&PID_0405&MI_02\\8&bf958f7&0&0000", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A70D&SUBSYS_8BB2103C&REV_00\\3&11583659&0&08", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,1,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:15", "first_install_time": "2025/7/29 4:35:41", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "ACPI Fixed Feature Button", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\FixedButton\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:02", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "ACPI Fixed Feature Button", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "PCI standard host CPU bridge", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A716&SUBSYS_8BB2103C&REV_00\\3&11583659&0&00", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,0,0)", "problem_status": "0x000000c0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI standard host CPU bridge", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_5253&PID_00A1&MI_02&Col01\\8&315edd0e&0&0000", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/19 21:56:00", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_5253&PID_00A1&MI_02&Col02\\8&315edd0e&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/19 21:56:00", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "ACPI Wake Alarm", "device_id": "(Standard system devices)", "hardware_id": "acpitime", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\ACPI000E\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "ACPI Wake Alarm", "driver_date": "10.0.22621.1", "driver_company": "acpitime.inf", "inf_file": "AcpiTime_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "ACPI Processor Aggregator", "device_id": "(Standard system devices)", "hardware_id": "acpipagr", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\ACPI000C\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "ACPI Processor Aggregator", "driver_date": "10.0.22621.1", "driver_company": "acpipagr.inf", "inf_file": "PagrInstall", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Plug and Play Software Device Enumerator", "device_id": "(Standard system devices)", "hardware_id": "swenum", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\SYSTEM\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:12", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "Plug and Play Software Device Enumerator", "driver_date": "10.0.22621.1", "driver_company": "swenum.inf", "inf_file": "SWENUM", "inf_section": "2022/5/6", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_5253&PID_1020&MI_02&Col01\\8&2c15cb32&0&0000", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "HID-compliant vendor-defined device", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_5253&PID_1020&MI_02&Col02\\8&2c15cb32&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant vendor-defined device", "driver_date": "10.0.22621.4111", "driver_company": "input.inf", "inf_file": "HID_Raw_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "PCI Express Root Port", "device_id": "(Standard system devices)", "hardware_id": "pci", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A74D&SUBSYS_00000000&REV_00\\3&11583659&0&30", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,6,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:41", "first_install_time": "2025/7/29 4:35:41", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "PCI Express Root Port", "driver_date": "10.0.22621.5262", "driver_company": "pci.inf", "inf_file": "PCI_BRIDGE", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Charge Arbitration Driver", "device_id": "(Standard system devices)", "hardware_id": "CAD", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\CAD\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "", "driver_description": "", "driver_version": "Charge Arbitration Driver", "driver_date": "10.0.22621.1", "driver_company": "ChargeArbitration.inf", "inf_file": "CAD_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "Microsoft System Management BIOS Driver", "device_id": "(Standard system devices)", "hardware_id": "mssmbios", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\mssmbios\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:10", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "", "driver_description": "", "driver_version": "Microsoft System Management BIOS Driver", "driver_date": "10.0.22621.1", "driver_company": "mssmbios.inf", "inf_file": "MSSMBIOS_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:08"}, {"device_name": "Generic Bus", "device_id": "(Standard system devices)", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0A05\\42", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Generic Bus", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV_X_PNP", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Unknown USB Device (Device Descriptor Request Failed)", "device_id": "(Standard USB Host Controller)", "hardware_id": "", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_0000&PID_0002\\6&379718c2&0&4", "problem_code": "Port_#0004.Hub_#0003", "problem_status": "0x00000064", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 8:47:54", "first_install_time": "2025/7/13 8:47:54", "firmware_revision": "", "device_class_guid": "{eecd61f7-5f70-11f0-8b1a-c85ea9dc6fcb}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/13 8:47:54", "device_type": "2025/7/13 8:47:54", "device_location": "2025/7/27 10:43:50", "driver_description": "2025/7/27 10:58:33", "driver_version": "Unknown USB Device (Device Descriptor Request Failed)", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "BADDEVICE.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/13 8:47:54"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_0D8C&PID_0343\\0123456789AB", "problem_code": "Port_#0002.Hub_#0002", "problem_status": "0x00000094", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_04F2&PID_B766\\01.00.00", "problem_code": "Port_#0006.Hub_#0002", "problem_status": "0x00000090", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:20", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_5253&PID_1020\\0123456789", "problem_code": "Port_#0004.Hub_#0003", "problem_status": "0x00000094", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_5253&PID_00A1\\0123456789A", "problem_code": "Port_#0004.Hub_#0003", "problem_status": "0x00000094", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/19 21:56:00", "first_install_time": "2025/7/27 19:24:43", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_1CA2&PID_0405\\1004863259", "problem_code": "Port_#0001.Hub_#0003", "problem_status": "0x00000094", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/18 1:31:38", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_18D1&PID_4EE8\\fb9504bc", "problem_code": "Port_#0002.Hub_#0003", "problem_status": "0x00000094", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:29:53", "first_install_time": "2025/7/12 23:29:53", "firmware_revision": "", "device_class_guid": "{c91e57d0-8949-52b7-bb02-e7dc8ea2b00a}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:29:53", "device_type": "2025/7/12 23:29:53", "device_location": "2025/7/13 1:33:20", "driver_description": "2025/7/13 1:34:09", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:29:53"}, {"device_name": "Standard Enhanced PCI to USB Host Controller", "device_id": "(Standard USB Host Controller)", "hardware_id": "us<PERSON><PERSON>ci", "compatible_id": "USB", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_0770&SUBSYS_077015AD&REV_00\\4&bbf9765&0&1088", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(2,2,0)", "problem_status": "0x00000006", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:06", "first_install_time": "2025/5/9 13:48:06", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "Standard Enhanced PCI to USB Host Controller", "driver_date": "10.0.22621.5262", "driver_company": "usbport.inf", "inf_file": "EHCI.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "Standard Universal PCI to USB Host Controller", "device_id": "(Standard USB Host Controller)", "hardware_id": "<PERSON><PERSON><PERSON><PERSON>", "compatible_id": "USB", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_0774&SUBSYS_197615AD&REV_00\\4&bbf9765&0&0088", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(2,0,0)", "problem_status": "0x00000006", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "Standard Universal PCI to USB Host Controller", "driver_date": "10.0.22621.5262", "driver_company": "usbport.inf", "inf_file": "UHCI.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "Unknown USB Device (Device Descriptor Request Failed)", "device_id": "(Standard USB Host Controller)", "hardware_id": "", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_0000&PID_0002\\5&37aff149&0&2", "problem_code": "Port_#0002.Hub_#0002", "problem_status": "0x00000064", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 20:07:05", "first_install_time": "2025/7/13 20:07:05", "firmware_revision": "", "device_class_guid": "{20be96f4-5fd8-11f0-8b1b-c85ea9dc6fcb}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/13 20:07:05", "device_type": "2025/7/13 20:07:05", "device_location": "2025/7/13 20:07:05", "driver_description": "2025/7/13 20:08:01", "driver_version": "Unknown USB Device (Device Descriptor Request Failed)", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "BADDEVICE.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/13 20:07:05"}, {"device_name": "USB Root Hub", "device_id": "(Standard USB Host Controller)", "hardware_id": "<PERSON><PERSON><PERSON>b", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\ROOT_HUB\\5&2891968b&0", "problem_code": "", "problem_status": "0x00000082", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:49:07", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "USB Root Hub", "driver_date": "10.0.22621.5262", "driver_company": "usbport.inf", "inf_file": "ROOTHUB.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:08"}, {"device_name": "USB Root Hub", "device_id": "(Standard USB Host Controller)", "hardware_id": "<PERSON><PERSON><PERSON>b", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\ROOT_HUB20\\5&36a4b5d6&0", "problem_code": "", "problem_status": "0x00000082", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:06", "first_install_time": "2025/5/9 13:49:07", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:06", "device_type": "2025/5/9 13:48:06", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "USB Root Hub", "driver_date": "10.0.22621.5262", "driver_company": "usbport.inf", "inf_file": "ROOTHUB.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:06"}, {"device_name": "Unknown USB Device (Port Reset Failed)", "device_id": "(Standard USB Host Controller)", "hardware_id": "", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_0000&PID_0001\\5&37aff149&0&13", "problem_code": "Port_#0013.Hub_#0002", "problem_status": "0x00000064", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:30:14", "first_install_time": "2025/7/12 23:30:14", "firmware_revision": "", "device_class_guid": "{b8b673af-5f33-11f0-8b15-644ed71b7dc9}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:30:14", "device_type": "2025/7/12 23:30:14", "device_location": "2025/7/13 1:30:34", "driver_description": "", "driver_version": "Unknown USB Device (Port Reset Failed)", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "BADDEVICE.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:30:14"}, {"device_name": "USB Composite Device", "device_id": "(Standard USB Host Controller)", "hardware_id": "usbccgp", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_0E0F&PID_0003\\6&39d724fe&0&5", "problem_code": "Port_#0005.Hub_#0003", "problem_status": "0x00000084", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:07", "first_install_time": "2025/5/9 13:48:07", "firmware_revision": "", "device_class_guid": "{8d9256e9-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:07", "device_type": "2025/5/9 13:48:07", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "USB Composite Device", "driver_date": "10.0.22621.5262", "driver_company": "usb.inf", "inf_file": "Composite.Dev.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:07"}, {"device_name": "USB Root Hub (USB 3.0)", "device_id": "(Standard USB HUBs)", "hardware_id": "USBHUB3", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\ROOT_HUB30\\5&11106705&0&0", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:06", "first_install_time": "2025/5/9 13:49:07", "firmware_revision": "", "device_class_guid": "{8d9256d8-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:06", "device_type": "2025/5/9 13:48:06", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "USB Root Hub (USB 3.0)", "driver_date": "10.0.22621.5262", "driver_company": "usbhub3.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/5/9 13:48:06"}, {"device_name": "USB Root Hub (USB 3.0)", "device_id": "(Standard USB HUBs)", "hardware_id": "USBHUB3", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\ROOT_HUB30\\4&388ffe47&0&0", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:08:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "USB Root Hub (USB 3.0)", "driver_date": "10.0.22621.5262", "driver_company": "usbhub3.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "Generic USB Hub", "device_id": "(Standard USB HUBs)", "hardware_id": "USBHUB3", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\VID_05E3&PID_0610\\5&37aff149&0&1", "problem_code": "Port_#0001.Hub_#0002", "problem_status": "0x00000084", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/13 1:25:39", "firmware_revision": "", "device_class_guid": "{9f4b56f0-1df6-11e0-ac64-0800200c9a66}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "Generic USB Hub", "driver_date": "10.0.22621.5262", "driver_company": "usbhub3.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "USB Root Hub (USB 3.0)", "device_id": "(Standard USB HUBs)", "hardware_id": "USBHUB3", "compatible_id": "USB", "instance_id": "", "service_name": "USB\\ROOT_HUB30\\4&70df8ff&0&0", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:08:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "USB Root Hub (USB 3.0)", "driver_date": "10.0.22621.5262", "driver_company": "usbhub3.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "Trusted Platform Module 2.0", "device_id": "(Standard)", "hardware_id": "TPM", "compatible_id": "SecurityDevices", "instance_id": "", "service_name": "ACPI\\MSFT0101\\1", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{d94ee5d8-d189-4994-83d2-f68d7d41b0e6}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Trusted Platform Module 2.0", "driver_date": "10.0.22621.5262", "driver_company": "tpm.inf", "inf_file": "Tpm2BaseInstall", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "@System32\\drivers\\usbxhci.sys,#1073807361;%1 USB %2 eXtensible Host Controller - %3 (Microsoft);(Standard,3.20,1.20)", "device_id": "Generic USB xHCI Host Controller", "hardware_id": "USBXHCI", "compatible_id": "USB", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_077A&SUBSYS_077A15AD&REV_00\\4&1ee266c4&0&00B0", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(11,0,0)", "problem_status": "0x00000006", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:06", "first_install_time": "2025/5/9 13:48:06", "firmware_revision": "", "device_class_guid": "{8d9256d8-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "USB xHCI Compliant Host Controller", "driver_date": "10.0.22621.5262", "driver_company": "usbxhci.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "@System32\\drivers\\usbxhci.sys,#1073807361;%1 USB %2 可扩展主机控制器 - %3 (Microsoft);(Intel(R),3.10,1.20)", "device_id": "Generic USB xHCI Host Controller", "hardware_id": "USBXHCI", "compatible_id": "USB", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51ED&SUBSYS_8BB2103C&REV_01\\3&11583659&0&A0", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,20,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "USB xHCI Compliant Host Controller", "driver_date": "10.0.22621.5262", "driver_company": "usbxhci.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "@System32\\drivers\\usbxhci.sys,#1073807361;%1 USB %2 可扩展主机控制器 - %3 (Microsoft);(Intel(R),3.20,1.20)", "device_id": "Generic USB xHCI Host Controller", "hardware_id": "USBXHCI", "compatible_id": "USB", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A71E&SUBSYS_00000000&REV_00\\3&11583659&0&68", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,13,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{36fc9e60-c465-11cf-8056-************}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "USB xHCI Compliant Host Controller", "driver_date": "10.0.22621.5262", "driver_company": "usbxhci.inf", "inf_file": "Generic.Install.NT", "inf_section": "2025/4/15", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Generic Bluetooth Adapter", "device_id": "GenericAdapter", "hardware_id": "BTHUSB", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "USB\\VID_0E0F&PID_0008\\000650268328", "problem_code": "Port_#0001.Hub_#0001", "problem_status": "0x00000014", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:11", "firmware_revision": "", "device_class_guid": "{2cbad689-8dc0-543b-a46f-dad20d391c7b}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/5/9 13:48:10", "device_type": "2025/5/9 13:48:10", "device_location": "2025/5/10 12:52:10", "driver_description": "", "driver_version": "Generic Bluetooth Adapter", "driver_date": "10.0.22621.5262", "driver_company": "bth.inf", "inf_file": "BthUsbGeneric.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:10"}, {"device_name": "Intel(R) Innovation Platform Framework Extensible Framework", "device_id": "Intel", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "SWD\\DRIVERENUM\\IpfEfExtComponent&4&15752cf2&0", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:07", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:07", "device_type": "2025/7/12 23:48:07", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Intel(R) Innovation Platform Framework Extensible Framework", "driver_date": "2.2.10200.11", "driver_company": "oem64.inf", "inf_file": "IpfEfInstall.NTamd64", "inf_section": "2024/7/19", "last_arrival_time": "2025/7/12 23:48:07"}, {"device_name": "Intel(R) Dynamic Tuning Technology Device Component", "device_id": "Intel", "hardware_id": "WUDFRd", "compatible_id": "SoftwareComponent", "instance_id": "", "service_name": "SWC\\VID8086_DTT_1.0\\4&15752cf2&0&00", "problem_code": "SWC", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:51:53", "first_install_time": "2025/7/29 4:35:51", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{5c4c3332-344d-483c-8739-259e934c9cc8}", "device_description": "2025/7/12 23:51:53", "device_type": "2025/7/12 23:48:09", "device_location": "2025/7/29 4:35:51", "driver_description": "", "driver_version": "Intel(R) Dynamic Tuning Technology", "driver_date": "9.0.11903.52187", "driver_company": "oem84.inf", "inf_file": "DttInstall_Win11_22621.NTamd64", "inf_section": "2024/10/23", "last_arrival_time": "2025/7/12 23:51:53"}, {"device_name": "Intel(R) Dynamic Tuning Technology Updater Component", "device_id": "Intel", "hardware_id": "", "compatible_id": "SoftwareComponent", "instance_id": "", "service_name": "SWC\\VID8086_DTTCFG_1.0\\4&15752cf2&0&01", "problem_code": "SWC", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:51:53", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{5c4c3332-344d-483c-8739-259e934c9cc8}", "device_description": "2025/7/12 23:51:53", "device_type": "2025/7/12 23:48:09", "device_location": "2025/7/29 4:35:51", "driver_description": "", "driver_version": "Intel(R) Dynamic Tuning Technology Updater Component", "driver_date": "9.0.11903.52187", "driver_company": "oem84.inf", "inf_file": "DttCfgInstall_Win11_22621.NTamd64", "inf_section": "2024/10/23", "last_arrival_time": "2025/7/12 23:51:53"}, {"device_name": "Intel(R) Innovation Platform Framework Processor Participant", "device_id": "Intel", "hardware_id": "ipf_cpu", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A71D&SUBSYS_8BB2103C&REV_00\\3&11583659&0&20", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI 总线 %1、设备 %2、功能 %3;(0,4,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:07", "first_install_time": "2025/7/29 4:35:49", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:07", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Intel(R) Innovation Platform Framework Processor Participant", "driver_date": "2.2.10200.11", "driver_company": "oem64.inf", "inf_file": "IpfInstall_Win11_22621.NTamd64", "inf_section": "2024/7/19", "last_arrival_time": "2025/7/12 23:48:07"}, {"device_name": "CPU to PCI Bridge", "device_id": "Intel", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_7190&SUBSYS_197615AD&REV_01\\3&61aaa01&0&00", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,0,0)", "problem_status": "0x000000c0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "CPU to PCI Bridge", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "NO_DRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Intel(R) 82371AB/EB PCI Bus Master IDE Controller", "device_id": "Intel", "hardware_id": "intelide", "compatible_id": "HDC", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_7111&SUBSYS_197615AD&REV_01\\3&61aaa01&0&39", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,7,1)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:03", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96a-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel(R) 82371AB/EB PCI Bus Master IDE Controller", "driver_date": "10.0.22621.2506", "driver_company": "mshdc.inf", "inf_file": "intelide_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "PCI to ISA Bridge", "device_id": "Intel", "hardware_id": "msisadrv", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_7110&SUBSYS_197615AD&REV_08\\3&61aaa01&0&38", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,7,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PCI to ISA Bridge", "driver_date": "10.0.22621.1", "driver_company": "machine.inf", "inf_file": "MSISADRV", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_2", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\b", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\a", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\9", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\8", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\7", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\6", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\5", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\4", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\3", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\2", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\1", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "13th Gen Intel(R) Core(TM) i5-13420H", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_186_-_13th_Gen_Intel(R)_Core(TM)_i5-13420H\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_7", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_6", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Intel(R) Management Engine Interface #1", "device_id": "Intel", "hardware_id": "MEIx64", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51E0&SUBSYS_8BB2103C&REV_01\\3&11583659&0&B0", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,22,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:06", "first_install_time": "2025/7/12 23:48:06", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:06", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) Management Engine Interface #1", "driver_date": "2452.7.1.0", "driver_company": "oem63.inf", "inf_file": "TEE_DDI_x64_NO_ICLS", "inf_section": "2024/12/22", "last_arrival_time": "2025/7/12 23:48:06"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_5", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Intel(R) SMBus - 51A3", "device_id": "INTEL", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51A3&SUBSYS_8BB2103C&REV_01\\3&11583659&0&FC", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,31,4)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:06", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) SMBus - 51A3", "driver_date": "*********", "driver_company": "oem59.inf", "inf_file": "Needs_NO_DRV", "inf_section": "", "last_arrival_time": "2025/7/12 23:48:06"}, {"device_name": "Intel(R) SPI (flash) Controller - 51A4", "device_id": "INTEL", "hardware_id": "", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51A4&SUBSYS_8BB2103C&REV_01\\3&11583659&0&FD", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,31,5)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:04", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) SPI (flash) Controller - 51A4", "driver_date": "*********", "driver_company": "oem59.inf", "inf_file": "Needs_NO_DRV", "inf_section": "", "last_arrival_time": "2025/7/12 23:48:03"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_4", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_3", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "device_id": "Intel", "hardware_id": "intelppm", "compatible_id": "Processor", "instance_id": "", "service_name": "ACPI\\GenuineIntel_-_Intel64_Family_6_Model_140_-_11th_Gen_Intel(R)_Core(TM)_i5-1135G7_@_2.40GHz\\_1", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{50127dc3-0f36-415e-a6cc-4cb3be910b65}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel Processor", "driver_date": "10.0.22621.5262", "driver_company": "cpu.inf", "inf_file": "IntelPPM_Inst.NT", "inf_section": "2009/4/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Intel(R) UHD Graphics", "device_id": "Intel Corporation", "hardware_id": "igfxn", "compatible_id": "Display", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A7A8&SUBSYS_8BB2103C&REV_04\\3&11583659&0&10", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,2,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:56", "first_install_time": "2025/7/29 4:35:46", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e968-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:56", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) UHD Graphics", "driver_date": "32.0.101.6877", "driver_company": "oem79.inf", "inf_file": "iRPLPD_w11_DS", "inf_section": "2025/6/4", "last_arrival_time": "2025/7/29 4:35:47"}, {"device_name": "Intel(R) Serial IO I2C Host Controller - 51E8", "device_id": "Intel Corporation", "hardware_id": "iaLPSS2_I2C_ADL", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51E8&SUBSYS_8BB2103C&REV_01\\3&11583659&0&A8", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,21,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:43", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:06", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) Serial IO I2C Host Controller - 51E8", "driver_date": "30.100.2221.20", "driver_company": "oem60.inf", "inf_file": "iaLPSS2_I2C_ADL_Device.NT", "inf_section": "2022/5/17", "last_arrival_time": "2025/7/12 23:48:05"}, {"device_name": "Intel(R) GNA Scoring Accelerator module", "device_id": "Intel Corporation", "hardware_id": "IntelGNA", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_A74F&SUBSYS_8BB2103C&REV_00\\3&11583659&0&40", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,8,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:12", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) GNA Scoring Accelerator module", "driver_date": "3.5.0.1578", "driver_company": "oem67.inf", "inf_file": "IntelGNA_Install.NT", "inf_section": "2023/11/14", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "Intel(R) Power Engine Plug-in", "device_id": "Intel Corporation", "hardware_id": "intelpep", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\INT33A1\\1", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:15", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) Power Engine Plug-in", "driver_date": "10.0.22621.5262", "driver_company": "intelpep.inf", "inf_file": "HSWULT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Intel(R) Wi-Fi 6E AX211 160MHz", "device_id": "Intel Corporation", "hardware_id": "Netwtw14", "compatible_id": "Net", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51F1&SUBSYS_00948086&REV_01\\3&11583659&0&A3", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,20,3)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:09:12", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:09", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) Wi-Fi 6E AX211 160MHz", "driver_date": "**********", "driver_company": "oem65.inf", "inf_file": "Install_GEN_SLR_GfP2_211_AX_6G_2x2_WinT", "inf_section": "2025/5/2", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Intel(R) Serial IO GPIO Host Controller - INTC1055", "device_id": "Intel Corporation", "hardware_id": "iaLPSS2_GPIO2_ADL", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\INTC1055\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:43", "first_install_time": "2025/7/12 23:48:04", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:04", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) Serial IO GPIO Host Controller - INTC1055", "driver_date": "30.100.2221.20", "driver_company": "oem61.inf", "inf_file": "iaLPSS2_GPIO2_ADL_Device.NT", "inf_section": "2022/5/17", "last_arrival_time": "2025/7/12 23:48:04"}, {"device_name": "英特尔(R) 无线 Bluetooth(R)", "device_id": "Intel Corporation", "hardware_id": "BTHUSB", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "USB\\VID_8087&PID_0033\\5&37aff149&0&10", "problem_code": "Port_#0010.Hub_#0002", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/29 4:35:44", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "英特尔(R) 无线 Bluetooth(R)", "driver_date": "**********", "driver_company": "oem68.inf", "inf_file": "ibtusb", "inf_section": "2025/4/29", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "Intel Tile Device", "device_id": "Intel Corporation", "hardware_id": "", "compatible_id": "", "instance_id": "", "service_name": "{2F2B7B01-597A-434C-8DD6-D27CD46EF73C}\\IntelTileDevice\\6&7e5936d&0", "problem_code": "Intel Tile Device", "problem_status": "0x00000060", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:14", "first_install_time": "2025/7/12 23:48:14", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:48:14", "device_location": "2025/7/29 4:35:45", "driver_description": "", "driver_version": "", "driver_date": "", "driver_company": "", "inf_file": "", "inf_section": "", "last_arrival_time": "2025/7/29 15:53:10"}, {"device_name": "Intel(R) 82574L Gigabit Network Connection", "device_id": "Intel Corporation", "hardware_id": "e1i68x64", "compatible_id": "Net", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_10D3&SUBSYS_07D015AD&REV_00\\000C29FFFF62DADA00", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(3,0,0)", "problem_status": "0x00000016", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:18", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{8d9256d7-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:19", "device_type": "2025/5/9 13:48:18", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Intel(R) 82574L Gigabit Network Connection", "driver_date": "**********", "driver_company": "net1ic64.inf", "inf_file": "E10D3.10.0.1", "inf_section": "2021/6/24", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Intel(R) HID Event Filter", "device_id": "Intel(R) Corporation", "hardware_id": "Hid<PERSON><PERSON><PERSON><PERSON><PERSON>", "compatible_id": "HIDClass", "instance_id": "", "service_name": "ACPI\\INTC1078\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Intel(R) HID Event Filter", "driver_date": "********", "driver_company": "oem66.inf", "inf_file": "HidEventFilter.NTamd64", "inf_section": "2024/4/25", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "英特尔? 智音技术检测验证", "device_id": "Intel(R) Corporation", "hardware_id": "WUDFRd", "compatible_id": "System", "instance_id": "", "service_name": "{DD8E82AE-334B-49A2-AEAE-AEB0FD5C40DD}\\DetectionVerification\\5&2d03116d&0&0", "problem_code": "Detection Notification", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:32", "first_install_time": "2025/7/29 4:35:48", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:51:54", "device_type": "2025/7/12 23:48:32", "device_location": "2025/7/29 4:35:48", "driver_description": "", "driver_version": "英特尔? 智音技术检测验证", "driver_date": "1.0.3837.0", "driver_company": "oem85.inf", "inf_file": "IntcDVWin11.NT", "inf_section": "2025/2/21", "last_arrival_time": "2025/7/12 23:51:54"}, {"device_name": "适用于数字麦克风的英特尔? 智音技术", "device_id": "Intel(R) Corporation", "hardware_id": "IntcDMic", "compatible_id": "MEDIA", "instance_id": "", "service_name": "INTELAUDIO\\CTLR_DEV_51CA&LINKTYPE_02&DEVTYPE_00&VEN_8086&DEV_AE20&SUBSYS_8BB2103C&REV_10EC\\5&2d03116d&0&0000", "problem_code": "Offload Engine Driver", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:32", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:33", "device_type": "2025/7/12 23:48:32", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "适用于数字麦克风的英特尔? 智音技术", "driver_date": "10.29.0.9467", "driver_company": "oem74.inf", "inf_file": "IntcAudModel.NT", "inf_section": "2023/7/18", "last_arrival_time": "2025/7/29 4:35:44"}, {"device_name": "适用于蓝牙? 音频的英特尔? 智音技术", "device_id": "Intel(R) Corporation", "hardware_id": "IntcBTAu", "compatible_id": "MEDIA", "instance_id": "", "service_name": "INTELAUDIO\\CTLR_DEV_51CA&LINKTYPE_03&DEVTYPE_00&VEN_8086&DEV_AE30&SUBSYS_8BB2103C&REV_0001\\5&2d03116d&0&0000", "problem_code": "Offload Engine Driver", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:32", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:32", "device_type": "2025/7/12 23:48:32", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "适用于蓝牙? 音频的英特尔? 智音技术", "driver_date": "10.29.0.9467", "driver_company": "oem75.inf", "inf_file": "IntcAudModel.NT", "inf_section": "2023/7/18", "last_arrival_time": "2025/7/12 23:48:32"}, {"device_name": "英特尔? 智音技术 OED", "device_id": "Intel(R) Corporation", "hardware_id": "IntcOED", "compatible_id": "System", "instance_id": "", "service_name": "INTELAUDIO\\DSP_CTLR_DEV_51CA&VEN_8086&DEV_0222&SUBSYS_8BB2103C&REV_0001\\4&13b0c171&0&0800", "problem_code": "HD Audio Bus Driver", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:26", "first_install_time": "2025/7/12 23:48:26", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:30", "device_type": "2025/7/12 23:48:26", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "英特尔? 智音技术 OED", "driver_date": "10.29.0.9467", "driver_company": "oem70.inf", "inf_file": "IntcOED_RPL.NT", "inf_section": "2023/7/18", "last_arrival_time": "2025/7/12 23:48:26"}, {"device_name": "英特尔? 智音技术总线", "device_id": "Intel(R) Corporation", "hardware_id": "IntcAudioBus", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_8086&DEV_51CA&SUBSYS_8BB2103C&REV_01\\3&11583659&0&FB", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(0,31,3)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:25", "first_install_time": "2025/7/12 23:48:25", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:25", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "英特尔? 智音技术总线", "driver_date": "10.29.0.9467", "driver_company": "oem69.inf", "inf_file": "IntcAudioBus.NT", "inf_section": "2023/7/18", "last_arrival_time": "2025/7/12 23:48:25"}, {"device_name": "High Definition Audio Controller", "device_id": "Microsoft", "hardware_id": "HDAudBus", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_1977&SUBSYS_197715AD&REV_09\\4&bbf9765&0&0888", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(2,1,0)", "problem_status": "0x00000006", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/10 12:52:08", "first_install_time": "2025/5/9 13:48:04", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "High Definition Audio Controller", "driver_date": "10.0.22621.5262", "driver_company": "hdaudbus.inf", "inf_file": "HDAudio_Device.NT", "inf_section": "2025/4/16", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "Microsoft Wi-Fi Direct Virtual Adapter #2", "device_id": "Microsoft", "hardware_id": "vwifimp", "compatible_id": "Net", "instance_id": "", "service_name": "{5d624f94-8850-40c3-a3fa-a4fd2080baf3}\\vwifimp_wfd\\4&37eca3da&0&12", "problem_code": "VWiFi Bus 0", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:11:01", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:11:01", "device_type": "2025/7/12 23:11:01", "device_location": "2025/7/12 23:49:58", "driver_description": "", "driver_version": "Microsoft Wi-Fi Direct Virtual Adapter", "driver_date": "10.0.22621.1", "driver_company": "netvwifimp.inf", "inf_file": "vwifimp_wfd.ndi", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Microsoft Wi-Fi Direct Virtual Adapter", "device_id": "Microsoft", "hardware_id": "vwifimp", "compatible_id": "Net", "instance_id": "", "service_name": "{5d624f94-8850-40c3-a3fa-a4fd2080baf3}\\vwifimp_wfd\\4&37eca3da&0&11", "problem_code": "VWiFi Bus 0", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:09:13", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:09:13", "device_type": "2025/7/12 23:09:13", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Microsoft Wi-Fi Direct Virtual Adapter", "driver_date": "10.0.22621.1", "driver_company": "netvwifimp.inf", "inf_file": "vwifimp_wfd.ndi", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Microsoft AC Adapter", "device_id": "Microsoft", "hardware_id": "CmBatt", "compatible_id": "Battery", "instance_id": "", "service_name": "ACPI\\ACPI0003\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{72631e54-78a4-11d0-bcf7-00aa00b7b32a}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft AC Adapter", "driver_date": "10.0.22621.1", "driver_company": "cmbatt.inf", "inf_file": "AcAdapter_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "I2C HID Device", "device_id": "Microsoft", "hardware_id": "hidi2c", "compatible_id": "HIDClass", "instance_id": "", "service_name": "ACPI\\ELAN0788\\4&45514d5&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:05", "device_type": "2025/7/12 23:48:05", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "I2C HID Device", "driver_date": "10.0.22621.2506", "driver_company": "hidi2c.inf", "inf_file": "hidi2c_Device.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:05"}, {"device_name": "Microsoft Basic Render Driver", "device_id": "Microsoft", "hardware_id": "BasicRender", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\BasicRender\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "", "driver_description": "", "driver_version": "Microsoft Basic Render Driver", "driver_date": "10.0.22621.2506", "driver_company": "basicrender.inf", "inf_file": "BasicRender", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:08"}, {"device_name": "GPIO Laptop or Slate Indicator Driver", "device_id": "Microsoft", "hardware_id": "msgpiowin32", "compatible_id": "HIDClass", "instance_id": "", "service_name": "ACPI\\INT33D3\\2&daba3ff&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "GPIO Laptop or Slate Indicator Driver", "driver_date": "10.0.22621.2506", "driver_company": "msgpiowin32.inf", "inf_file": "GPIO_Inst_Sensors.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Composite Bus Enumerator", "device_id": "Microsoft", "hardware_id": "CompositeBus", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\CompositeBus\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "Composite Bus Enumerator", "driver_date": "10.0.22621.1", "driver_company": "compositebus.inf", "inf_file": "CompositeBus_Device.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Microsoft Kernel Debug Network Adapter", "device_id": "Microsoft", "hardware_id": "kdnic", "compatible_id": "Net", "instance_id": "", "service_name": "ROOT\\KDNIC\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:12", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:10", "device_type": "2025/5/9 13:48:10", "device_location": "2025/5/9 13:48:12", "driver_description": "", "driver_version": "Microsoft Kernel Debug Network Adapter", "driver_date": "10.0.22621.1", "driver_company": "kdnic.inf", "inf_file": "KdNic.ndi", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "High Definition Audio Controller", "device_id": "Microsoft", "hardware_id": "HDAudBus", "compatible_id": "System", "instance_id": "", "service_name": "PCI\\VEN_10DE&DEV_2291&SUBSYS_8BB2103C&REV_A1\\4&17846016&0&0108", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(1,0,1)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/29 4:35:43", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "High Definition Audio Controller", "driver_date": "10.0.22621.5262", "driver_company": "hdaudbus.inf", "inf_file": "HDAudio_Device.NT", "inf_section": "2025/4/16", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "NDIS Virtual Network Adapter Enumerator", "device_id": "Microsoft", "hardware_id": "NdisVirtualBus", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\NdisVirtualBus\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:55", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "NDIS Virtual Network Adapter Enumerator", "driver_date": "10.0.22621.1", "driver_company": "ndisvirtualbus.inf", "inf_file": "NdisVirtualBus_Device.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Remote Desktop Device Redirector Bus", "device_id": "Microsoft", "hardware_id": "rdpbus", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\RDPBUS\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:55", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "Remote Desktop Device Redirector Bus", "driver_date": "10.0.22621.2506", "driver_company": "rdpbus.inf", "inf_file": "RDPBUS", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Microsoft Storage Spaces Controller", "device_id": "Microsoft", "hardware_id": "spaceport", "compatible_id": "SCSIAdapter", "instance_id": "", "service_name": "ROOT\\spaceport\\0000", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:04", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "", "driver_description": "", "driver_version": "Microsoft Storage Spaces Controller", "driver_date": "10.0.22621.5262", "driver_company": "spaceport.inf", "inf_file": "Spaceport_Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "HID-compliant consumer control device", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_5253&PID_1020&MI_01&Col02\\8&18e5aef4&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant consumer control device", "driver_date": "10.0.22621.1", "driver_company": "hidserv.inf", "inf_file": "HIDSystemConsumerDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "UMBus Root Bus Enumerator", "device_id": "Microsoft", "hardware_id": "umbus", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\UMBUS\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "UMBus Root Bus Enumerator", "driver_date": "10.0.22621.2506", "driver_company": "umbus.inf", "inf_file": "UmBusRoot_Device.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "NVIDIA Virtual Audio Device (Wave Extensible) (WDM)", "device_id": "Microsoft", "hardware_id": "nvvad_WaveExtensible", "compatible_id": "MEDIA", "instance_id": "", "service_name": "ROOT\\UNNAMED_DEVICE\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:51:26", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:51:26", "device_type": "2025/7/12 23:51:26", "device_location": "2025/7/12 23:51:26", "driver_description": "", "driver_version": "NVIDIA Virtual Audio Device (Wave Extensible) (WDM)", "driver_date": "********", "driver_company": "oem82.inf", "inf_file": "NVVAD_WaveExtensible64.NT", "inf_section": "2024/2/28", "last_arrival_time": "2025/7/12 23:51:26"}, {"device_name": "Microsoft Virtual Drive Enumerator", "device_id": "Microsoft", "hardware_id": "vdrvroot", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\vdrvroot\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:04", "first_install_time": "2025/5/9 13:48:04", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "", "driver_description": "", "driver_version": "Microsoft Virtual Drive Enumerator", "driver_date": "10.0.22621.1", "driver_company": "vdrvroot.inf", "inf_file": "VDRVROOT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "Microsoft Hyper-V Virtualization Infrastructure Driver", "device_id": "Microsoft", "hardware_id": "Vid", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\VID\\0000", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "", "driver_description": "", "driver_version": "Microsoft Hyper-V Virtualization Infrastructure Driver", "driver_date": "10.0.22621.5262", "driver_company": "wvid.inf", "inf_file": "Vid_Device_Client.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "Volume Manager", "device_id": "Microsoft", "hardware_id": "volmgr", "compatible_id": "System", "instance_id": "", "service_name": "ROOT\\volmgr\\0000", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:04", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-FFFF-FFFFFFFFFFFF}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "", "driver_description": "", "driver_version": "Volume Manager", "driver_date": "10.0.22621.2506", "driver_company": "volmgr.inf", "inf_file": "Volmgr", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "HID-compliant mouse", "device_id": "Microsoft", "hardware_id": "mou<PERSON>d", "compatible_id": "Mouse", "instance_id": "", "service_name": "HID\\VID_5253&PID_1020&MI_00\\8&5b592b6&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{d8f8bd6a-3570-5223-9cf1-7f4309046903}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "HID_Mouse_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "HID-compliant consumer control device", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_5253&PID_00A1&MI_01&Col02\\8&8a0a89f&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/19 21:56:00", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "HID-compliant consumer control device", "driver_date": "10.0.22621.1", "driver_company": "hidserv.inf", "inf_file": "HIDSystemConsumerDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "Volume", "device_id": "Microsoft", "hardware_id": "volume", "compatible_id": "Volume", "instance_id": "", "service_name": "STORAGE\\Volume\\{fbc71803-5f38-11f0-8b16-806e6f6e6963}#0000000000200000", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:58:00", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{71a27cdd-812a-11d0-bec7-08002be2092f}", "device_description": "2025/7/12 23:58:00", "device_type": "2025/7/12 23:58:00", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Volume", "driver_date": "10.0.22621.1", "driver_company": "volume.inf", "inf_file": "volume_install.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:58:00"}, {"device_name": "Volume", "device_id": "Microsoft", "hardware_id": "volume", "compatible_id": "Volume", "instance_id": "", "service_name": "STORAGE\\Volume\\{fbc71803-5f38-11f0-8b16-806e6f6e6963}#0000000012E00000", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:58:00", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{71a27cdd-812a-11d0-bec7-08002be2092f}", "device_description": "2025/7/12 23:58:00", "device_type": "2025/7/12 23:58:00", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Volume", "driver_date": "10.0.22621.1", "driver_company": "volume.inf", "inf_file": "volume_install.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:58:00"}, {"device_name": "Volume", "device_id": "Microsoft", "hardware_id": "volume", "compatible_id": "Volume", "instance_id": "", "service_name": "STORAGE\\Volume\\{fbc71803-5f38-11f0-8b16-806e6f6e6963}#0000000013E00000", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:58:00", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{71a27cdd-812a-11d0-bec7-08002be2092f}", "device_description": "2025/7/12 23:58:00", "device_type": "2025/7/12 23:58:00", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Volume", "driver_date": "10.0.22621.1", "driver_company": "volume.inf", "inf_file": "volume_install.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:58:00"}, {"device_name": "Volume", "device_id": "Microsoft", "hardware_id": "volume", "compatible_id": "Volume", "instance_id": "", "service_name": "STORAGE\\Volume\\{fbc71803-5f38-11f0-8b16-806e6f6e6963}#0000001E14000000", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:58:00", "first_install_time": "2025/7/12 23:58:00", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{71a27cdd-812a-11d0-bec7-08002be2092f}", "device_description": "2025/7/12 23:58:00", "device_type": "2025/7/12 23:58:00", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Volume", "driver_date": "10.0.22621.1", "driver_company": "volume.inf", "inf_file": "volume_install.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:58:00"}, {"device_name": "Microsoft Streaming Service Proxy", "device_id": "Microsoft", "hardware_id": "MSKSSRV", "compatible_id": "MEDIA", "instance_id": "", "service_name": "SW\\{96E080C7-143C-11D1-B40F-00A0C9223196}\\{3C0D501A-140B-11D1-B40F-00A0C9223196}", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:50:12", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:12", "device_type": "2025/5/9 13:50:12", "device_location": "2025/7/29 4:35:49", "driver_description": "2025/7/29 4:36:42", "driver_version": "Microsoft Streaming Service Proxy", "driver_date": "10.0.22621.1", "driver_company": "ksfilter.inf", "inf_file": "MSKSSRV.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:50:12"}, {"device_name": "Microsoft Streaming Tee/Sink-to-Sink Converter", "device_id": "Microsoft", "hardware_id": "MSTEE", "compatible_id": "MEDIA", "instance_id": "", "service_name": "SW\\{cfd669f1-9bc2-11d0-8299-0000f822fe8a}\\{0A4252A0-7E70-11D0-A5D6-28DB04C10000}", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/22 1:39:44", "first_install_time": "2025/7/22 1:39:44", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/22 1:39:44", "device_type": "2025/7/22 1:39:44", "device_location": "2025/7/22 1:39:44", "driver_description": "2025/7/22 1:40:00", "driver_version": "Microsoft Streaming Tee/Sink-to-Sink Converter", "driver_date": "10.0.22621.1", "driver_company": "ksfilter.inf", "inf_file": "MSTEE.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/22 1:39:44"}, {"device_name": "Microsoft Streaming Tee/Sink-to-Sink Converter", "device_id": "Microsoft", "hardware_id": "MSTEE", "compatible_id": "MEDIA", "instance_id": "", "service_name": "SW\\{cfd669f1-9bc2-11d0-8299-0000f822fe8a}\\{CF1DDA2C-9743-11D0-A3EE-00A0C9223196}", "problem_code": "", "problem_status": "0x000000b0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/22 1:39:45", "first_install_time": "2025/7/22 1:39:45", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/22 1:39:45", "device_type": "2025/7/22 1:39:45", "device_location": "2025/7/22 1:39:45", "driver_description": "2025/7/22 1:40:00", "driver_version": "Microsoft Streaming Tee/Sink-to-Sink Converter", "driver_date": "10.0.22621.1", "driver_company": "ksfilter.inf", "inf_file": "MSTEE.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/22 1:39:45"}, {"device_name": "HID-compliant mouse", "device_id": "Microsoft", "hardware_id": "mou<PERSON>d", "compatible_id": "Mouse", "instance_id": "", "service_name": "HID\\VID_5253&PID_00A1&MI_00\\8&310b5668&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/20 0:23:54", "first_install_time": "2025/7/19 21:56:00", "firmware_revision": "", "device_class_guid": "{c2d5b8a3-0535-51f7-b454-e006b332ac05}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/19 21:56:00", "device_type": "2025/7/19 21:56:00", "device_location": "2025/7/27 19:24:43", "driver_description": "2025/7/28 2:30:23", "driver_version": "HID-compliant mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "HID_Mouse_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/19 21:56:00"}, {"device_name": "HID-compliant mouse", "device_id": "Microsoft", "hardware_id": "mou<PERSON>d", "compatible_id": "Mouse", "instance_id": "", "service_name": "HID\\VID_1CA2&PID_0405&MI_01&Col04\\8&17dde4cb&0&0003", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "HID_Mouse_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "HP Victus by HP Gaming Laptop 15-fa1xxx", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "Computer", "instance_id": "", "service_name": "SWD\\COMPUTER\\MFG_HP&FAM_103C_5335M7_HP_Victus&PROD_Victus_by_HP_Gaming_Laptop_15-fa1xx&SKU_7N6X6PA#AB2", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e966-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Computer Device", "driver_date": "10.0.22621.1", "driver_company": "compdev.inf", "inf_file": "CompDev_Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "VMware, Inc. VMware20,1", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "Computer", "instance_id": "", "service_name": "SWD\\COMPUTER\\MFG_VMware__Inc.&PROD_VMware20_1", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e966-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Computer Device", "driver_date": "10.0.22621.1", "driver_company": "compdev.inf", "inf_file": "CompDev_Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "HID-compliant consumer control device", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_1CA2&PID_0405&MI_01&Col02\\8&17dde4cb&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{cc17fe81-e908-50bd-bec9-0c7d7b43548d}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "HID-compliant consumer control device", "driver_date": "10.0.22621.1", "driver_company": "hidserv.inf", "inf_file": "HIDSystemConsumerDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "Generic software component", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareComponent", "instance_id": "", "service_name": "SWD\\DRIVERENUM\\OEM_DAL_component&4&bb3716d&0", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:06", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{5c4c3332-344d-483c-8739-259e934c9cc8}", "device_description": "2025/7/12 23:48:06", "device_type": "2025/7/12 23:48:06", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "Generic software component", "driver_date": "10.0.22621.1", "driver_company": "c_swcomponent.inf", "inf_file": "SoftwareComponent", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:06"}, {"device_name": "Generic software component", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareComponent", "instance_id": "", "service_name": "SWD\\DRIVERENUM\\OEM_WMI_component&4&bb3716d&0", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:06", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{5c4c3332-344d-483c-8739-259e934c9cc8}", "device_description": "2025/7/12 23:48:06", "device_type": "2025/7/12 23:48:06", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "Generic software component", "driver_date": "10.0.22621.1", "driver_company": "c_swcomponent.inf", "inf_file": "SoftwareComponent", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:06"}, {"device_name": "Microsoft GS Wavetable Synth", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\MicrosoftGSWavetableSynth", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000001", "connected": "Yes", "safe_removal": "No", "install_time": "2025/5/9 13:48:18", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/5/9 13:48:18", "device_type": "2025/5/9 13:48:18", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:18"}, {"device_name": "MIDI function [0]", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\MIDII_8D64D185.P_0000", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:29:53", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{c91e57d0-8949-52b7-bb02-e7dc8ea2b00a}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/7/12 23:29:53", "device_type": "2025/7/12 23:29:53", "device_location": "2025/7/13 1:33:20", "driver_description": "2025/7/13 1:34:09", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:29:53"}, {"device_name": "MIDI function [1]", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\MIDII_8D64D185.P_0001", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:29:53", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{c91e57d0-8949-52b7-bb02-e7dc8ea2b00a}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/7/12 23:29:53", "device_type": "2025/7/12 23:29:53", "device_location": "2025/7/13 1:33:20", "driver_description": "2025/7/13 1:34:09", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:29:53"}, {"device_name": "24G15N (NVIDIA High Definition Audio)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.0.00000000}.{6a62f949-6627-4ebf-91b7-fbd12d8ded23}", "problem_code": "", "problem_status": "0x000000d4", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:58:11", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{ed1238ed-ba48-51fd-a50e-41753a9b1ef0}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/7/12 23:58:11", "device_type": "2025/7/12 23:58:11", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/7/12 23:58:11"}, {"device_name": "Headphones (MCHOSE V9 PRO HEADSET)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.0.00000000}.{e287bd11-c21a-4d63-81f4-98fc805d8685}", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:18", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "24G15N (NVIDIA High Definition Audio)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.0.00000000}.{e3bafd47-6718-4d66-9222-4ece8a8a2264}", "problem_code": "", "problem_status": "0x000000d4", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:51:25", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{518c4278-4817-51e0-8c74-adb9d4177f17}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/7/12 23:51:25", "device_type": "2025/7/12 23:51:25", "device_location": "2025/7/12 23:51:25", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/7/12 23:51:25"}, {"device_name": "Speakers (High Definition Audio Device)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.0.00000000}.{e3f5e8f1-8ef7-472f-9e44-a04fe1752770}", "problem_code": "", "problem_status": "0x000000d4", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:18", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{3e2a39ea-00b9-5acb-aa41-de48e9aaa52c}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/5/9 13:48:18", "device_type": "2025/5/9 13:48:18", "device_location": "2025/5/10 12:52:12", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/5/9 13:48:18"}, {"device_name": "麦克风阵列 (适用于数字麦克风的英特尔? 智音技术)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.1.00000000}.{2fc66427-6487-4fa0-9183-5f95c7ca06ad}", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:34", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/7/12 23:48:34", "device_type": "2025/7/12 23:48:34", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/7/12 23:48:34"}, {"device_name": "Microphone (High Definition Audio Device)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.1.00000000}.{509b72a4-5b19-4b61-be24-4c3ef827a492}", "problem_code": "", "problem_status": "0x000000d4", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:18", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{7bda2d6e-4477-5ed4-b71d-2717950a2211}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/5/9 13:48:18", "device_type": "2025/5/9 13:48:18", "device_location": "2025/5/10 12:52:12", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/5/9 13:48:18"}, {"device_name": "Microphone (MCHOSE V9 PRO HEADSET)", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "AudioEndpoint", "instance_id": "", "service_name": "SWD\\MMDEVAPI\\{0.0.1.00000000}.{e9c36e74-93d3-4543-b02a-133f027fd625}", "problem_code": "", "problem_status": "0x000000d0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:18", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "Audio Endpoint", "driver_date": "10.0.22621.1", "driver_company": "audioendpoint.inf", "inf_file": "NO_DRV", "inf_section": "2022/5/6", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "Microsoft Device Association Root Enumerator", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\MSDAS\\{ce958e9a-424f-4c88-86f4-11314821e75a}", "problem_code": "", "problem_status": "0x000000f0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:18", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/5/9 13:48:18", "device_type": "2025/5/9 13:48:18", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:18"}, {"device_name": "WAN Miniport (IKEv2)", "device_id": "Microsoft", "hardware_id": "RasAgileVpn", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_AGILEVPNMINIPORT", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:41", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:41", "device_type": "2025/5/9 13:50:41", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (IKEv2)", "driver_date": "10.0.22621.1", "driver_company": "netavpna.inf", "inf_file": "Ndi-Mp-AgileVpn", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (L2TP)", "device_id": "Microsoft", "hardware_id": "Rasl2tp", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_L2TPMINIPORT", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:41", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:41", "device_type": "2025/5/9 13:50:41", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (L2TP)", "driver_date": "10.0.22621.1", "driver_company": "netrasa.inf", "inf_file": "Ndi-Mp-L2tp", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (Network Monitor)", "device_id": "Microsoft", "hardware_id": "NdisWan", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_NDISWANBH", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:42", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:42", "device_type": "2025/5/9 13:50:42", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (Network Monitor)", "driver_date": "10.0.22621.1", "driver_company": "netrasa.inf", "inf_file": "Ndi-Mp-Bh", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (IP)", "device_id": "Microsoft", "hardware_id": "NdisWan", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_NDISWANIP", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:42", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:42", "device_type": "2025/5/9 13:50:42", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (IP)", "driver_date": "10.0.22621.1", "driver_company": "netrasa.inf", "inf_file": "Ndi-Mp-Ip", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (IPv6)", "device_id": "Microsoft", "hardware_id": "NdisWan", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_NDISWANIPV6", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:42", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:42", "device_type": "2025/5/9 13:50:42", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (IPv6)", "driver_date": "10.0.22621.1", "driver_company": "netrasa.inf", "inf_file": "Ndi-Mp-Ipv6", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (PPPOE)", "device_id": "Microsoft", "hardware_id": "RasPppoe", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_PPPOEMINIPORT", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:42", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:42", "device_type": "2025/5/9 13:50:42", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (PPPOE)", "driver_date": "10.0.22621.1", "driver_company": "netrasa.inf", "inf_file": "Ndi-Mp-Pppoe", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (PPTP)", "device_id": "Microsoft", "hardware_id": "PptpMiniport", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_PPTPMINIPORT", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:42", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:41", "device_type": "2025/5/9 13:50:41", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (PPTP)", "driver_date": "10.0.22621.1", "driver_company": "netrasa.inf", "inf_file": "Ndi-Mp-Pptp", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "WAN Miniport (SSTP)", "device_id": "Microsoft", "hardware_id": "RasSstp", "compatible_id": "Net", "instance_id": "", "service_name": "SWD\\MSRRAS\\MS_SSTPMINIPORT", "problem_code": "", "problem_status": "0x00000010", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:50:40", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:50:40", "device_type": "2025/5/9 13:50:40", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "WAN Miniport (SSTP)", "driver_date": "10.0.22621.1", "driver_company": "netsstpa.inf", "inf_file": "Ndi-Mp-Sstp", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Microsoft RRAS Root Enumerator", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\MSRRAS\\{5e259276-bc7e-40e3-b93b-8f89b5f3abc0}", "problem_code": "", "problem_status": "0x000000f0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:50:40", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/5/9 13:50:40", "device_type": "2025/5/9 13:50:40", "device_location": "2025/7/29 8:01:55", "driver_description": "", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:50:40"}, {"device_name": "Root Print Queue", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "PrintQueue", "instance_id": "", "service_name": "SWD\\PRINTENUM\\PrintQueues", "problem_code": "", "problem_status": "0x000000f0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:51:14", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{1ed2bbf9-11f0-4084-b21f-ad83a8e6dcdc}", "device_description": "2025/5/9 13:51:14", "device_type": "2025/5/9 13:51:14", "device_location": "2025/5/9 13:51:14", "driver_description": "", "driver_version": "Local Print Queue", "driver_date": "10.0.22621.1", "driver_company": "printqueue.inf", "inf_file": "NO_DRV_LOCAL", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:51:14"}, {"device_name": "蓝牙", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\RADIO\\Bluetooth_c85ea9dc6fcb", "problem_code": "", "problem_status": "0x000000f0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:14", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:48:14", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:14"}, {"device_name": "Microsoft Radio Device Enumeration Bus", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\RADIO\\{3DB5895D-CC28-44B3-AD3D-6F01A782B8D2}", "problem_code": "", "problem_status": "0x000000f0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:49:25", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/5/9 13:49:25", "device_type": "2025/5/9 13:49:25", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:49:25"}, {"device_name": "WLAN", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "SWD\\RADIO\\{83A1D67C-DDC3-46B2-9F0F-9406CAFCEF81}", "problem_code": "", "problem_status": "0x000000f0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:09:13", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/7/12 23:09:13", "device_type": "2025/7/12 23:09:13", "device_location": "2025/7/29 4:35:49", "driver_description": "", "driver_version": "Generic software device", "driver_date": "10.0.22621.1", "driver_company": "c_swdevice.inf", "inf_file": "SoftwareDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:09:13"}, {"device_name": "System Firmware", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "Firmware", "instance_id": "", "service_name": "UEFI\\RES_{f2f290c7-71e2-458d-801b-3e0c3df19019}\\0", "problem_code": "", "problem_status": "0x00000070", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{f2e7dd72-6468-4e36-b6f1-6488f42c1b52}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "System Firmware", "driver_date": "10.0.22621.1", "driver_company": "c_firmware.inf", "inf_file": "FirmwareResource.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "HID-compliant mouse", "device_id": "Microsoft", "hardware_id": "mou<PERSON>d", "compatible_id": "Mouse", "instance_id": "", "service_name": "HID\\VID_0E0F&PID_0003&MI_01\\8&34ace767&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{8d9256e9-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "2025/5/10 12:52:10", "driver_description": "", "driver_version": "HID-compliant mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "HID_Mouse_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:08"}, {"device_name": "HID-compliant mouse", "device_id": "Microsoft", "hardware_id": "mou<PERSON>d", "compatible_id": "Mouse", "instance_id": "", "service_name": "HID\\VID_0E0F&PID_0003&MI_00\\8&217ccb29&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{8d9256e9-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:08", "device_type": "2025/5/9 13:48:08", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "HID-compliant mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "HID_Mouse_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:08"}, {"device_name": "HID-compliant consumer control device", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\VID_0D8C&PID_0343&MI_00&Col01\\7&1c49d3c4&0&0000", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:07:17", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:07:17", "device_type": "2025/7/12 23:07:17", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "HID-compliant consumer control device", "driver_date": "10.0.22621.1", "driver_company": "hidserv.inf", "inf_file": "HIDSystemConsumerDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:17"}, {"device_name": "Portable Device Control device", "device_id": "Microsoft", "hardware_id": "buttonconverter", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\INTC816&Col02\\3&1535a34c&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:48:12", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "Portable Device Control device", "driver_date": "10.0.22621.1", "driver_company": "buttonconverter.inf", "inf_file": "btnconv.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "Microsoft Input Configuration Device", "device_id": "Microsoft", "hardware_id": "MTConfig", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\ELAN0788&Col04\\5&34a72ad8&0&0003", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:05", "device_type": "2025/7/12 23:48:05", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "Microsoft Input Configuration Device", "driver_date": "10.0.22621.1", "driver_company": "mtconfig.inf", "inf_file": "MTConfigInst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:05"}, {"device_name": "HID-compliant mouse", "device_id": "Microsoft", "hardware_id": "mou<PERSON>d", "compatible_id": "Mouse", "instance_id": "", "service_name": "HID\\ELAN0788&Col01\\5&34a72ad8&0&0000", "problem_code": "", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:05", "device_type": "2025/7/12 23:48:05", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "HID-compliant mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "HID_Mouse_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:05"}, {"device_name": "HID-compliant consumer control device", "device_id": "Microsoft", "hardware_id": "", "compatible_id": "HIDClass", "instance_id": "", "service_name": "HID\\ConvertedDevice&Col02\\5&1feb78c6&0&0001", "problem_code": "", "problem_status": "0x000000e0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:48:12", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "HID-compliant consumer control device", "driver_date": "10.0.22621.1", "driver_company": "hidserv.inf", "inf_file": "HIDSystemConsumerDevice", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "High Definition Audio Device", "device_id": "Microsoft", "hardware_id": "HdAudAddService", "compatible_id": "MEDIA", "instance_id": "", "service_name": "HDAUDIO\\FUNC_01&VEN_15AD&DEV_1975&SUBSYS_15AD1975&REV_1001\\5&217be3d6&0&0001", "problem_code": "Internal High Definition Audio Bus", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:11", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:10", "device_type": "2025/5/9 13:48:10", "device_location": "2025/5/10 12:52:09", "driver_description": "", "driver_version": "High Definition Audio Device", "driver_date": "10.0.22621.2506", "driver_company": "hdaudio.inf", "inf_file": "HdAudModel", "inf_section": "2023/10/19", "last_arrival_time": "2025/5/10 12:52:09"}, {"device_name": "High Definition Audio Device", "device_id": "Microsoft", "hardware_id": "HdAudAddService", "compatible_id": "MEDIA", "instance_id": "", "service_name": "HDAUDIO\\FUNC_01&VEN_10EC&DEV_0245&SUBSYS_103C8BB2&REV_1000\\4&13b0c171&0&0001", "problem_code": "HD Audio Bus Driver", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:48:30", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:30", "device_type": "2025/7/12 23:48:30", "device_location": "2025/7/12 23:48:30", "driver_description": "2025/7/12 23:48:30", "driver_version": "High Definition Audio Device", "driver_date": "10.0.22621.2506", "driver_company": "hdaudio.inf", "inf_file": "HdAudModel", "inf_section": "2023/10/19", "last_arrival_time": "2025/7/12 23:48:30"}, {"device_name": "HP Wide Vision HD Camera", "device_id": "Microsoft", "hardware_id": "usbvideo", "compatible_id": "Camera", "instance_id": "", "service_name": "USB\\VID_04F2&PID_B766&MI_00\\6&28651dbc&0&0000", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x000000a4", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:19", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{ca3e7ab9-b4c3-4ae6-8251-579ef933890f}", "device_description": "2025/7/12 23:07:19", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:44", "driver_description": "", "driver_version": "USB Video Device", "driver_date": "10.0.22621.5262", "driver_company": "usbvideo.inf", "inf_file": "USBVideo.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "NVIDIA High Definition Audio", "device_id": "Microsoft", "hardware_id": "NVHDA", "compatible_id": "MEDIA", "instance_id": "", "service_name": "HDAUDIO\\FUNC_01&VEN_10DE&DEV_00A0&SUBSYS_103C8BB2&REV_1001\\5&25db88aa&0&0001", "problem_code": "Internal High Definition Audio Bus", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:51:22", "first_install_time": "2025/7/12 23:48:05", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:51:25", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "NVIDIA High Definition Audio", "driver_date": "*******", "driver_company": "oem81.inf", "inf_file": "WIN11NVHDA64V", "inf_section": "2024/9/28", "last_arrival_time": "2025/7/12 23:51:25"}, {"device_name": "Converted Portable Device Control device", "device_id": "Microsoft", "hardware_id": "mshidkmdf", "compatible_id": "HIDClass", "instance_id": "", "service_name": "ButtonConverter\\ConvertedDevice\\4&21a236ab&0&0", "problem_code": "", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/13 0:06:41", "first_install_time": "2025/7/12 23:48:12", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{745a17a0-74d3-11d0-b6fe-00a0c90f57da}", "device_description": "2025/7/12 23:48:12", "device_type": "2025/7/12 23:48:12", "device_location": "2025/7/29 4:35:43", "driver_description": "", "driver_version": "Converted Portable Device Control device", "driver_date": "10.0.22621.1", "driver_company": "buttonconverter.inf", "inf_file": "btnconv_converted.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:12"}, {"device_name": "Bluetooth Device (RFCOMM Protocol TDI)", "device_id": "Microsoft", "hardware_id": "RFCOMM", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "BTH\\MS_RFCOMM\\7&20f38eb4&0&0", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:11", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{2cbad689-8dc0-543b-a46f-dad20d391c7b}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/5/9 13:48:11", "device_type": "2025/5/9 13:48:11", "device_location": "2025/5/9 13:50:24", "driver_description": "", "driver_version": "Bluetooth Device (RFCOMM Protocol TDI)", "driver_date": "10.0.22621.2506", "driver_company": "tdibth.inf", "inf_file": "RFCOMM.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:11"}, {"device_name": "MCHOSE V9 PRO HEADSET", "device_id": "Microsoft", "hardware_id": "<PERSON><PERSON><PERSON><PERSON>", "compatible_id": "MEDIA", "instance_id": "", "service_name": "USB\\VID_0D8C&PID_0343&MI_01\\6&18a8239b&0&0001", "problem_code": "0000.0014.0000.***************.000.000", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:18", "first_install_time": "2025/7/12 23:07:18", "firmware_revision": "", "device_class_guid": "{366634a1-ee05-5ae1-9a48-51712c3e43b0}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:18", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 5:10:15", "driver_description": "", "driver_version": "USB Audio Device", "driver_date": "10.0.22621.5262", "driver_company": "wdma_usb.inf", "inf_file": "USBAudio", "inf_section": "2025/4/16", "last_arrival_time": "2025/7/12 23:07:18"}, {"device_name": "Bluetooth Device (RFCOMM Protocol TDI)", "device_id": "Microsoft", "hardware_id": "RFCOMM", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "BTH\\MS_RFCOMM\\6&7e5936d&0&0", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:14", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:48:14", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "Bluetooth Device (RFCOMM Protocol TDI)", "driver_date": "10.0.22621.2506", "driver_company": "tdibth.inf", "inf_file": "RFCOMM.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:14"}, {"device_name": "Bluetooth Device (Personal Area Network)", "device_id": "Microsoft", "hardware_id": "BthPan", "compatible_id": "Net", "instance_id": "", "service_name": "BTH\\MS_BTHPAN\\7&20f38eb4&0&2", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:18", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{2cbad689-8dc0-543b-a46f-dad20d391c7b}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:18", "device_type": "2025/5/9 13:48:18", "device_location": "2025/5/9 13:50:24", "driver_description": "", "driver_version": "Bluetooth Device (Personal Area Network)", "driver_date": "10.0.22621.2506", "driver_company": "bthpan.inf", "inf_file": "BthPan.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Bluetooth Device (Personal Area Network) #2", "device_id": "Microsoft", "hardware_id": "BthPan", "compatible_id": "Net", "instance_id": "", "service_name": "BTH\\MS_BTHPAN\\6&7e5936d&0&2", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:17", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:48:14", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "Bluetooth Device (Personal Area Network)", "driver_date": "10.0.22621.2506", "driver_company": "bthpan.inf", "inf_file": "BthPan.Install", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Microsoft Bluetooth LE Enumerator", "device_id": "Microsoft", "hardware_id": "BthLEEnum", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "BTH\\MS_BTHLE\\6&7e5936d&0&3", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:14", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:48:14", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "Microsoft Bluetooth LE Enumerator", "driver_date": "10.0.22621.5262", "driver_company": "bthleenum.inf", "inf_file": "BthLEEnum.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:14"}, {"device_name": "Microsoft Bluetooth Enumerator", "device_id": "Microsoft", "hardware_id": "BthEnum", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "BTH\\MS_BTHBRB\\7&20f38eb4&0&1", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:11", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{2cbad689-8dc0-543b-a46f-dad20d391c7b}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/5/9 13:48:11", "device_type": "2025/5/9 13:48:11", "device_location": "2025/5/9 13:50:24", "driver_description": "", "driver_version": "Microsoft Bluetooth Enumerator", "driver_date": "10.0.22621.5262", "driver_company": "bth.inf", "inf_file": "BthEnum.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:11"}, {"device_name": "Microsoft Bluetooth Enumerator", "device_id": "Microsoft", "hardware_id": "BthEnum", "compatible_id": "Bluetooth", "instance_id": "", "service_name": "BTH\\MS_BTHBRB\\6&7e5936d&0&1", "problem_code": "", "problem_status": "0x00000080", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:48:14", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}", "device_description": "2025/7/12 23:48:14", "device_type": "2025/7/12 23:48:14", "device_location": "2025/7/29 4:35:46", "driver_description": "", "driver_version": "Microsoft Bluetooth Enumerator", "driver_date": "10.0.22621.5262", "driver_company": "bth.inf", "inf_file": "BthEnum.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:48:14"}, {"device_name": "MIDI function", "device_id": "Microsoft", "hardware_id": "<PERSON><PERSON><PERSON><PERSON>", "compatible_id": "MEDIA", "instance_id": "", "service_name": "USB\\VID_18D1&PID_4EE8&MI_00\\7&5558c9c&0&0000", "problem_code": "0000.0014.0000.001.***************.000", "problem_status": "0x000000a0", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:29:53", "first_install_time": "2025/7/12 23:29:53", "firmware_revision": "", "device_class_guid": "{c91e57d0-8949-52b7-bb02-e7dc8ea2b00a}", "device_manufacturer": "{4d36e96c-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:29:53", "device_type": "2025/7/12 23:29:53", "device_location": "2025/7/13 1:33:20", "driver_description": "2025/7/13 1:34:09", "driver_version": "USB Audio Device", "driver_date": "10.0.22621.5262", "driver_company": "wdma_usb.inf", "inf_file": "USBAudio", "inf_section": "2025/4/16", "last_arrival_time": "2025/7/12 23:29:53"}, {"device_name": "Microsoft UEFI-Compliant System", "device_id": "Microsoft", "hardware_id": "UEFI", "compatible_id": "System", "instance_id": "", "service_name": "ACPI_HAL\\UEFI\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft UEFI-Compliant System", "driver_date": "10.0.22621.1", "driver_company": "uefi.inf", "inf_file": "UEFI_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft ACPI-Compliant System", "device_id": "Microsoft", "hardware_id": "ACPI", "compatible_id": "System", "instance_id": "", "service_name": "ACPI_HAL\\PNP0C08\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/5/9 13:48:02", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft ACPI-Compliant System", "driver_date": "10.0.22621.5262", "driver_company": "acpi.inf", "inf_file": "ACPI_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "PS/2 Compatible Mouse", "device_id": "Microsoft", "hardware_id": "i8042prt", "compatible_id": "Mouse", "instance_id": "", "service_name": "ACPI\\VMW0003\\4&1bd7f811&0", "problem_code": "", "problem_status": "0x00000020", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:03", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96f-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "PS/2 Compatible Mouse", "driver_date": "10.0.22621.1", "driver_company": "msmouse.inf", "inf_file": "PS2_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Microsoft Hyper-V Generation Counter", "device_id": "Microsoft", "hardware_id": "gencounter", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\VMW0001\\7", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:08", "first_install_time": "2025/5/9 13:48:08", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:03", "device_type": "2025/5/9 13:48:03", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Microsoft Hyper-V Generation Counter", "driver_date": "10.0.22621.1", "driver_company": "wgencounter.inf", "inf_file": "VmGenCounter.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:03"}, {"device_name": "Microsoft ACPI-Compliant Control Method Battery", "device_id": "Microsoft", "hardware_id": "CmBatt", "compatible_id": "Battery", "instance_id": "", "service_name": "ACPI\\PNP0C0A\\1", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{72631e54-78a4-11d0-bcf7-00aa00b7b32a}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft ACPI-Compliant Control Method Battery", "driver_date": "10.0.22621.1", "driver_company": "cmbatt.inf", "inf_file": "CmBatt_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "UCM-UCSI ACPI Device", "device_id": "Microsoft", "hardware_id": "UcmUcsiAcpiClient", "compatible_id": "UCM", "instance_id": "", "service_name": "ACPI\\USBC000\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{e6f1aa1c-7f3b-4473-b2e8-c97d8ac71d53}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "UCM-UCSI ACPI Device", "driver_date": "10.0.22621.5262", "driver_company": "UcmUcsiAcpiClient.inf", "inf_file": "UcmUcsiAcpiClient.Install.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft Windows Management Interface for ACPI", "device_id": "Microsoft", "hardware_id": "WmiAcpi", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C14\\TestDev", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft Windows Management Interface for ACPI", "driver_date": "10.0.22621.5262", "driver_company": "wmiacpi.inf", "inf_file": "WMIMAP_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft Windows Management Interface for ACPI", "device_id": "Microsoft", "hardware_id": "WmiAcpi", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C14\\DSarDev", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft Windows Management Interface for ACPI", "driver_date": "10.0.22621.5262", "driver_company": "wmiacpi.inf", "inf_file": "WMIMAP_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft Windows Management Interface for ACPI", "device_id": "Microsoft", "hardware_id": "WmiAcpi", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C14\\cPSWMI", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft Windows Management Interface for ACPI", "driver_date": "10.0.22621.5262", "driver_company": "wmiacpi.inf", "inf_file": "WMIMAP_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft Windows Management Interface for ACPI", "device_id": "Microsoft", "hardware_id": "WmiAcpi", "compatible_id": "System", "instance_id": "", "service_name": "ACPI\\PNP0C14\\0", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:17", "first_install_time": "2025/7/12 23:07:15", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97d-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:15", "device_type": "2025/7/12 23:07:15", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Microsoft Windows Management Interface for ACPI", "driver_date": "10.0.22621.5262", "driver_company": "wmiacpi.inf", "inf_file": "WMIMAP_Inst.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:15"}, {"device_name": "Microsoft AC Adapter", "device_id": "Microsoft", "hardware_id": "CmBatt", "compatible_id": "Battery", "instance_id": "", "service_name": "ACPI\\ACPI0003\\1", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:10", "first_install_time": "2025/5/9 13:48:02", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{72631e54-78a4-11d0-bcf7-00aa00b7b32a}", "device_description": "2025/5/9 13:48:02", "device_type": "2025/5/9 13:48:02", "device_location": "2025/5/10 12:52:02", "driver_description": "", "driver_version": "Microsoft AC Adapter", "driver_date": "10.0.22621.1", "driver_company": "cmbatt.inf", "inf_file": "AcAdapter_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:02"}, {"device_name": "NVIDIA GeForce RTX 2050", "device_id": "NVIDIA", "hardware_id": "nvlddmkm", "compatible_id": "Display", "instance_id": "", "service_name": "PCI\\VEN_10DE&DEV_25AD&SUBSYS_8BB2103C&REV_A1\\4&17846016&0&0008", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(1,0,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:51:21", "first_install_time": "2025/7/29 4:35:48", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e968-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:51:21", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "NVIDIA GeForce RTX 2050", "driver_date": "32.0.15.7688", "driver_company": "oem80.inf", "inf_file": "Section125", "inf_section": "2025/6/24", "last_arrival_time": "2025/7/29 16:52:39"}, {"device_name": "NVIDIA Platform Controllers and Framework", "device_id": "NVIDIA", "hardware_id": "nvpcf", "compatible_id": "SoftwareDevice", "instance_id": "", "service_name": "ACPI\\NVDA0820\\NPCF", "problem_code": "", "problem_status": "0x00000030", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:51:26", "first_install_time": "2025/7/12 23:51:26", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{62f9c741-b25a-46ce-b54c-9bccce08b6f2}", "device_description": "2025/7/12 23:51:26", "device_type": "2025/7/12 23:07:18", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "NVIDIA Platform Controllers and Framework", "driver_date": "32.0.15.7582", "driver_company": "oem83.inf", "inf_file": "nvpcf_Device.NT", "inf_section": "2025/3/30", "last_arrival_time": "2025/7/12 23:51:26"}, {"device_name": "一加 Ace 2", "device_id": "OnePlus", "hardware_id": "WUDFWpdMtp", "compatible_id": "WPD", "instance_id": "", "service_name": "USB\\VID_22D9&PID_2764\\fb9504bc", "problem_code": "Port_#0002.Hub_#0003", "problem_status": "0x00000094", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/7/12 23:29:57", "first_install_time": "2025/7/12 23:58:06", "firmware_revision": "", "device_class_guid": "{2769b2cd-ef22-594b-bb0a-68b19526a2af}", "device_manufacturer": "{eec5ad98-8080-425f-922a-dabf3de3f69a}", "device_description": "2025/7/12 23:29:58", "device_type": "2025/7/12 23:29:58", "device_location": "2025/7/12 23:58:06", "driver_description": "2025/7/13 0:03:05", "driver_version": "一加 Ace 2", "driver_date": "10.0.22621.5262", "driver_company": "wpdmtp.inf", "inf_file": "MTP.NT", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:29:57"}, {"device_name": "Realtek Gaming GbE Family Controller", "device_id": "Realtek", "hardware_id": "rt640x64", "compatible_id": "Net", "instance_id": "", "service_name": "PCI\\VEN_10EC&DEV_8168&SUBSYS_8BB2103C&REV_16\\4&9580661&0&00E7", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(4,0,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:20", "first_install_time": "2025/7/12 23:48:31", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e972-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:48:32", "device_type": "2025/7/12 23:07:20", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Realtek Gaming GbE Family Controller", "driver_date": "10.75.324.2025", "driver_company": "oem78.inf", "inf_file": "RTL8118AS.ndi.NT", "inf_section": "2025/3/24", "last_arrival_time": "2025/7/29 4:35:56"}, {"device_name": "Standard NVM Express Controller", "device_id": "Standard NVM Express Controller", "hardware_id": "stornvme", "compatible_id": "SCSIAdapter", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07F0&SUBSYS_07F015AD&REV_00\\4&3b50545d&0&00B8", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(19,0,0)", "problem_status": "0x00000006", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:06", "first_install_time": "2025/5/9 13:48:06", "firmware_revision": "", "device_class_guid": "{8d9256d9-2ca1-11f0-8b0c-806e6f6e6963}", "device_manufacturer": "{4d36e97b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "Standard NVM Express Controller", "driver_date": "10.0.22621.5262", "driver_company": "stornvme.inf", "inf_file": "Stornvme_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}, {"device_name": "Standard NVM Express Controller", "device_id": "Standard NVM Express Controller", "hardware_id": "stornvme", "compatible_id": "SCSIAdapter", "instance_id": "", "service_name": "PCI\\VEN_144D&DEV_A80A&SUBSYS_A801144D&REV_00\\4&2ae848f5&0&0030", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(2,0,0)", "problem_status": "0x00000000", "disabled": "0x00000000", "connected": "No", "safe_removal": "Yes", "install_time": "2025/7/12 23:07:16", "first_install_time": "2025/7/12 23:07:16", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e97b-e325-11ce-bfc1-08002be10318}", "device_description": "2025/7/12 23:07:16", "device_type": "2025/7/12 23:07:16", "device_location": "2025/7/29 4:35:41", "driver_description": "", "driver_version": "Standard NVM Express Controller", "driver_date": "10.0.22621.5262", "driver_company": "stornvme.inf", "inf_file": "Stornvme_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/7/12 23:07:16"}, {"device_name": "Standard SATA AHCI Controller", "device_id": "Standard SATA AHCI Controller", "hardware_id": "s<PERSON><PERSON><PERSON>", "compatible_id": "HDC", "instance_id": "", "service_name": "PCI\\VEN_15AD&DEV_07E0&SUBSYS_07E015AD&REV_00\\4&bbf9765&0&1888", "problem_code": "@System32\\drivers\\pci.sys,#65536;PCI bus %1, device %2, function %3;(2,3,0)", "problem_status": "0x00000006", "disabled": "0x00000000", "connected": "No", "safe_removal": "No", "install_time": "2025/5/9 13:48:04", "first_install_time": "2025/5/9 13:48:04", "firmware_revision": "", "device_class_guid": "{00000000-0000-0000-ffff-ffffffffffff}", "device_manufacturer": "{4d36e96a-e325-11ce-bfc1-08002be10318}", "device_description": "2025/5/9 13:48:04", "device_type": "2025/5/9 13:48:04", "device_location": "2025/5/10 12:52:03", "driver_description": "", "driver_version": "Standard SATA AHCI Controller", "driver_date": "10.0.22621.2506", "driver_company": "mshdc.inf", "inf_file": "msahci_Inst", "inf_section": "2006/6/21", "last_arrival_time": "2025/5/9 13:48:04"}]