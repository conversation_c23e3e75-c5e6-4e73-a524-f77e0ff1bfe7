[{"filename": "2025/7/12 23:07:18", "extension": "Blue Screen", "full_path": "071325-7453-01.dmp", "action_time": "C:\\Windows\\Minidump\\071325-7453-01.dmp", "action_type": "MEMORY_MANAGEMENT   0x0000001a (00000000`00008886 ffff9200`0c662ac0 ffff9200`00000030 00000000`00000400)", "process_name": "dmp", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 6:38:34", "extension": "Blue Screen", "full_path": "071325-7109-01.dmp", "action_time": "C:\\Windows\\Minidump\\071325-7109-01.dmp", "action_type": "MEMORY_MANAGEMENT   0x0000001a (00000000`00008886 ffffe080`00981030 ffffe080`00000030 00000000`00000400)", "process_name": "dmp", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 18:57:16", "extension": "Blue Screen", "full_path": "071325-7500-01.dmp", "action_time": "C:\\Windows\\Minidump\\071325-7500-01.dmp", "action_type": "MEMORY_MANAGEMENT   0x0000001a (00000000`00008886 ffffa480`0b37a9e0 ffffa480`00000030 00000000`00000400)", "process_name": "dmp", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 21:21:53", "extension": "Blue Screen", "full_path": "071425-7265-01.dmp", "action_time": "C:\\Windows\\Minidump\\071425-7265-01.dmp", "action_type": "DRIVER_VERIFIER_DMA_VIOLATION   0x000000e6 (00000000`00000026 00000000`00000000 00000000`00126bd2 00000000`00000005)", "process_name": "dmp", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/14 19:55:18", "extension": "Blue Screen", "full_path": "072625-8109-01.dmp", "action_time": "C:\\Windows\\Minidump\\072625-8109-01.dmp", "action_type": "SYSTEM_SERVICE_EXCEPTION   0x0000003b (00000000`c0000005 fffff803`2fcf572e ffff8605`ececeba0 00000000`00000000)", "process_name": "dmp", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:34:41", "extension": "Select file in open/save dialog-box", "full_path": "ToDesk_Lite.exe", "action_time": "C:\\Users\\<USER>\\Downloads\\ToDesk_Lite.exe", "action_type": "", "process_name": "exe", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\ComDlg32\\OpenSavePidlMRU\\exe", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/15 2:46:11", "extension": "Select file in open/save dialog-box", "full_path": "7ÔÂ15ÈÕ.mp4", "action_time": "D:\\ËØ²Ä\\³ÉÆ·\\7ÔÂ15ÈÕ.mp4", "action_type": "", "process_name": "mp4", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\ComDlg32\\OpenSavePidlMRU\\mp4", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 10:40:09", "extension": "Select file in open/save dialog-box", "full_path": "********************************.jpg", "action_time": "C:\\Users\\<USER>\\Desktop\\********************************.jpg", "action_type": "", "process_name": "jpg", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\ComDlg32\\OpenSavePidlMRU\\jpg", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 12:08:25", "extension": "Select file in open/save dialog-box", "full_path": "ÆÁÄ»½ØÍ¼ 2025-07-16 194613.png", "action_time": "C:\\Users\\<USER>\\Pictures\\screenshots\\ÆÁÄ»½ØÍ¼ 2025-07-16 194613.png", "action_type": "", "process_name": "png", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\ComDlg32\\OpenSavePidlMRU\\*", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 12:08:25", "extension": "Select file in open/save dialog-box", "full_path": "ÆÁÄ»½ØÍ¼ 2025-07-16 194613.png", "action_time": "C:\\Users\\<USER>\\Pictures\\screenshots\\ÆÁÄ»½ØÍ¼ 2025-07-16 194613.png", "action_type": "", "process_name": "png", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\ComDlg32\\OpenSavePidlMRU\\png", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:55", "extension": "Software Crash", "full_path": "updater.exe", "action_time": "D:\\À×Éñ\\LeiGod_Acc\\resources\\leishenSdk\\updater.exe", "action_type": "updater.exe, 11.2.0.1, 68787172, updater.exe, 11.2.0.1, 68787172, c0000005, 00103c2f, 0x3f80, 0x1dbfd91d4476d3c, D:\\À×Éñ\\LeiGod_Acc\\resources\\leishenSdk\\updater.exe, D:\\À×Éñ\\LeiGod_Acc\\resources\\leishenSdk\\updater.exe, a80d4db2-b7bd-40a3-9b48-c668758e9c23, ,", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "MobileOptionPack", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MobileOptionPack", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Fontcore", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Fontcore", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "IE40", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\IE40", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "IE4Data", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\IE4Data", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "IE5BAKEX", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\IE5BAKEX", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "IEData", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\IEData", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Connection Manager", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Connection Manager", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "AddressBook", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\AddressBook", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "WIC", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\WIC", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "DirectDrawEx", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\DirectDrawEx", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 13:27:59", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "SchedulingAgent", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SchedulingAgent", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 15:39:43", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "MPlayer2", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MPlayer2", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2022/5/7 15:39:43", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "DXM_Runtime", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\DXM_Runtime", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:28", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2005 Redistributable", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{710f4c1c-cc18-4c49-8cbf-51240c89a1a2}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:31", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2008 Redistributable - x86 9.0.30729.6161", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{9BE518E6-ECC6-35A9-88E4-87755C07200F}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:39", "extension": "Software Installation", "full_path": "vcredist_x86.exe", "action_time": "C:\\ProgramData\\Package Cache\\{33d1fd90-4274-48a1-9bc1-97e33d9c2d6f}\\vcredist_x86.exe", "action_type": "Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.61030", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{33d1fd90-4274-48a1-9bc1-97e33d9c2d6f}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:40", "extension": "Software Installation", "full_path": "vcredist_x64.exe", "action_time": "C:\\ProgramData\\Package Cache\\{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}\\vcredist_x64.exe", "action_type": "Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:41", "extension": "Software Installation", "full_path": "vcredist_x86.exe", "action_time": "C:\\ProgramData\\Package Cache\\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}\\vcredist_x86.exe", "action_type": "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:42", "extension": "Software Installation", "full_path": "vcredist_x64.exe", "action_time": "C:\\ProgramData\\Package Cache\\{042d26ef-3dbe-4c25-95d3-4c1b11b235a7}\\vcredist_x64.exe", "action_type": "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.40664", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{042d26ef-3dbe-4c25-95d3-4c1b11b235a7}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2467173", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2467173", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2151757", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2151757", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB982573", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB982573", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2524860", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2524860", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2608539", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2608539", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2549743", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2549743", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2544655", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2544655", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2565063", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2565063", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2821701", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2821701", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2723430", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2723430", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2845370", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2845370", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2689322", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2689322", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2890375", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}.KB2890375", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2010  x86 Redistributable - 10.0.40219", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2467173", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2467173", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2151757", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2151757", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB982573", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB982573", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2689322", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2689322", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2608539", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2608539", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2821701", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2821701", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2890375", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2890375", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2845370", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2845370", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2723430", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2723430", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2012 x86 Additional Runtime - 11.0.61030", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{B175520C-86A2-35A7-8619-86DC379688B9}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2012 x86 Minimum Runtime - 11.0.61030", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{BD95A8CD-1D9F-35AD-981A-3E7925026EBB}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Software Installation", "full_path": "vcredist_x86.exe", "action_time": "C:\\ProgramData\\Package Cache\\{a55ac379-46b0-461a-95b1-fef5c08443f2}\\vcredist_x86.exe", "action_type": "Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.61030", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{a55ac379-46b0-461a-95b1-fef5c08443f2}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Software Installation", "full_path": "vcredist_x64.exe", "action_time": "C:\\ProgramData\\Package Cache\\{9bd48a22-fe5a-457c-8f10-da6c2be89eee}\\vcredist_x64.exe", "action_type": "Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{9bd48a22-fe5a-457c-8f10-da6c2be89eee}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2013 x86 Additional Runtime - 12.0.40664", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{D401961D-3A20-3AC7-943B-6139D5BD490A}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2013 x86 Minimum Runtime - 12.0.40664", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{8122DAB1-ED4D-3676-BB0A-CA368196543E}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Software Installation", "full_path": "vcredist_x86.exe", "action_time": "C:\\ProgramData\\Package Cache\\{21f70a0b-e7bf-43cf-96d8-c6145bc5c269}\\vcredist_x86.exe", "action_type": "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{21f70a0b-e7bf-43cf-96d8-c6145bc5c269}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:03", "extension": "Software Installation", "full_path": "vcredist_x64.exe", "action_time": "C:\\ProgramData\\Package Cache\\{e1daf4c4-2b9a-4140-8c2b-b96d404f54b3}\\vcredist_x64.exe", "action_type": "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.40664", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{e1daf4c4-2b9a-4140-8c2b-b96d404f54b3}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:31:23", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "DF Launcher", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\DF Launcher", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:45", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Èý½ÇÖÞÐÐ¶¯", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Èý½ÇÖÞÐÐ¶¯", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:54", "extension": "Software Installation", "full_path": "delta_force_launcher.exe", "action_time": "D:\\Èý½ÇÖÞ\\Delta Force\\launcher\\delta_force_launcher.exe", "action_type": "Èý½ÇÖÞÐÐ¶¯", "process_name": "exe", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\delta_force_launcher", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:40:15", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Edge Update", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft Edge Update", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:21", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2524860", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2524860", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:21", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2544655", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2544655", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:21", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2549743", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2549743", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:21", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2565063", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{1D8E6291-B0D5-35EC-8441-6616F567A0F7}.KB2565063", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:25", "extension": "Software Installation", "full_path": "UE4PrereqSetup_x64.exe", "action_time": "C:\\ProgramData\\Package Cache\\{4e242cc8-5e3c-4b08-9d55-dbc62ddd1208}\\UE4PrereqSetup_x64.exe", "action_type": "UE4 Prerequisites (x64)", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{4e242cc8-5e3c-4b08-9d55-dbc62ddd1208}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 8:41:11", "extension": "Software Installation", "full_path": "Battle.net.exe", "action_time": "D:\\ÊØÍûÏÈ·æ\\Battle.net\\Battle.net.exe", "action_type": "±©Ñ©Õ½Íø", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Battle.net", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 8:47:29", "extension": "Software Installation", "full_path": "app.ico", "action_time": "D:\\kook\\KOOK\\app.ico", "action_type": "KOOK", "process_name": "ico", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\KOOK", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 9:19:02", "extension": "Software Installation", "full_path": "NeacLoader.exe", "action_time": "D:\\ÊØÍûÏÈ·æ\\Overwatch\\_retail_\\NeacLoader.exe", "action_type": "ÊØÍûÏÈ·æ", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Overwatch", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 20:53:52", "extension": "Software Installation", "full_path": "Uninstall.exe", "action_time": "D:\\yy\\Uninstall.exe", "action_type": "YY9", "process_name": "exe", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\YY9", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 20:54:27", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "360se6", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\360se6", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 20:54:30", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "mostool", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\mostool", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/15 1:49:54", "extension": "Software Installation", "full_path": "uninst.exe", "action_time": "C:\\Users\\<USER>\\AppData\\Local\\JianyingPro\\Apps\\uninst.exe", "action_type": "¼ôÓ³×¨Òµ°æ", "process_name": "exe", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\JianyingPro", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/15 3:16:05", "extension": "Software Installation", "full_path": "Weixin.exe", "action_time": "D:\\Î¢ÐÅ\\Weixin\\Weixin.exe", "action_type": "Î¢ÐÅ", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Weixin", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:32:50", "extension": "Software Installation", "full_path": "uninstall.exe", "action_time": "D:\\steam\\uninstall.exe", "action_type": "Steam", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:27", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2022 X86 Minimum Runtime - 14.44.35211", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{922480B5-CAEB-4B1B-AAA4-9716EFDCE26B}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:28", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "Microsoft Visual C++ 2022 X86 Additional Runtime - 14.44.35211", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{C18FB403-1E88-43C8-AD8A-CED50F23DE8B}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:29", "extension": "Software Installation", "full_path": "VC_redist.x86.exe", "action_time": "C:\\ProgramData\\Package Cache\\{0b5169e3-39da-4313-808e-1f9c0407f3bf}\\VC_redist.x86.exe", "action_type": "Microsoft Visual C++ 2015-2022 Redistributable (x86) - 14.44.35211", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{0b5169e3-39da-4313-808e-1f9c0407f3bf}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:38", "extension": "Software Installation", "full_path": "VC_redist.x64.exe", "action_time": "C:\\ProgramData\\Package Cache\\{d8bbe9f9-7c5b-42c6-b715-9ee898a2e515}\\VC_redist.x64.exe", "action_type": "Microsoft Visual C++ 2015-2022 Redistributable (x64) - 14.44.35211", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{d8bbe9f9-7c5b-42c6-b715-9ee898a2e515}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/19 9:08:31", "extension": "Software Installation", "full_path": "wegame.exe", "action_time": "D:\\wegame\\wegame.exe", "action_type": "WeGame", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\WeGame", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/19 9:40:52", "extension": "Software Installation", "full_path": "", "action_time": "", "action_type": "ºÍÆ½¾«Ó¢PCÄ£ÄâÆ÷", "process_name": "", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\ºÍÆ½¾«Ó¢PCÄ£ÄâÆ÷", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 6:37:50", "extension": "Software Installation", "full_path": "douyin.exe", "action_time": "D:\\¶¶Òô\\douyin\\douyin.exe", "action_type": "¶¶Òô", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\douyin", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:02:10", "extension": "Software Installation", "full_path": "cloudmusic.exe", "action_time": "D:\\ÍøÒ×ÔÆ\\CloudMusic\\cloudmusic.exe", "action_type": "ÍøÒ×ÔÆÒôÀÖ", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\ÍøÒ×ÔÆÒôÀÖ", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:43:08", "extension": "Software Installation", "full_path": "quark.exe", "action_time": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Quark\\quark.exe", "action_type": "¿ä¿Ë", "process_name": "exe", "process_id": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{FB7670C2-7F99-426D-B687-21BB585A5C73}_is1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 6:46:22", "extension": "Software Installation", "full_path": "msedgewebview2.exe", "action_time": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.109\\msedgewebview2.exe", "action_type": "Microsoft Edge WebView2 Runtime", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Microsoft EdgeWebView", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 8:00:16", "extension": "Software Installation", "full_path": "EAappInstaller.exe", "action_time": "C:\\ProgramData\\Package Cache\\{f4804bb6-be22-404b-aea5-d9d561a5cf7d}\\EAappInstaller.exe", "action_type": "EA app", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{f4804bb6-be22-404b-aea5-d9d561a5cf7d}", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 16:52:34", "extension": "Software Installation", "full_path": "ToDesk.exe", "action_time": "C:\\Program Files\\ToDesk\\ToDesk.exe", "action_type": "ToDesk", "process_name": "exe", "process_id": "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\ToDesk", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:14", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:05", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:57:43", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:34", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:24:59", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:29:36", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:01", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:26", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:49:42", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:34", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:13", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:14", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:21", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:28", "extension": "System Shutdown", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:18", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:25", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:22", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:58:06", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:52", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:25:31", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:29:59", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 6:38:37", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 18:57:19", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 21:21:56", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/14 19:55:20", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:33", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:45", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:50:02", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:56", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:39", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:33", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 11:59:06", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 11:07:13", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:39", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 6:01:07", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:49", "extension": "System Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 13:49:41", "extension": "Task Run", "full_path": "MdmDiagnosticsTool.exe", "action_time": "C:\\Windows\\system32\\MdmDiagnosticsTool.exe", "action_type": "MdmDiagnosticsCleanup, \\Microsoft\\Windows\\Management\\Provisioning\\MdmDiagnosticsCleanup", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 13:50:35", "extension": "Task Run", "full_path": "", "action_time": "", "action_type": "CreateObjectTask, \\Microsoft\\Windows\\CloudExperienceHost\\CreateObjectTask", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:06:55", "extension": "Task Run", "full_path": "mscoree.dll", "action_time": "C:\\Windows\\System32\\mscoree.dll", "action_type": ".NET Framework NGEN v4.0.30319 Critical, \\Microsoft\\Windows\\.NET Framework\\.NET Framework NGEN v4.0.30319 Critical", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:06:55", "extension": "Task Run", "full_path": "mscoree.dll", "action_time": "C:\\Windows\\System32\\mscoree.dll", "action_type": ".NET Framework NGEN v4.0.30319 64 Critical, \\Microsoft\\Windows\\.NET Framework\\.NET Framework NGEN v4.0.30319 64 Critical", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:20:40", "extension": "Task Run", "full_path": "usoclient.exe", "action_time": "C:\\Windows\\system32\\usoclient.exe", "action_type": "StartOobeAppsScanAfterUpdate, \\Microsoft\\Windows\\UpdateOrchestrator\\StartOobeAppsScanAfterUpdate", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:17:51", "extension": "Task Run", "full_path": "directxdatabaseupdater.exe", "action_time": "C:\\Windows\\system32\\directxdatabaseupdater.exe", "action_type": "DirectXDatabaseUpdater, \\Microsoft\\Windows\\DirectX\\DirectXDatabaseUpdater", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:01:07", "extension": "Task Run", "full_path": "dimsjob.dll", "action_time": "C:\\Windows\\system32\\dimsjob.dll", "action_type": "UserTask-Roam, \\Microsoft\\Windows\\CertificateServicesClient\\UserTask-Roam", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:48:48", "extension": "Task Run", "full_path": "TpmTasks.dll", "action_time": "C:\\Windows\\system32\\TpmTasks.dll", "action_type": "Sqm-Tasks, \\Microsoft\\Windows\\PI\\Sqm-Tasks", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:48:48", "extension": "Task Run", "full_path": "LanguageComponentsInstaller.dll", "action_time": "C:\\Windows\\System32\\LanguageComponentsInstaller.dll", "action_type": "Uninstallation, \\Microsoft\\Windows\\LanguageComponentsInstaller\\Uninstallation", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:48:48", "extension": "Task Run", "full_path": "rundll32.exe", "action_time": "C:\\Windows\\system32\\rundll32.exe", "action_type": "Pre-staged app cleanup, \\Microsoft\\Windows\\AppxDeploymentClient\\Pre-staged app cleanup", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/19 0:38:55", "extension": "Task Run", "full_path": "DeviceSetupManagerAPI.dll", "action_time": "C:\\Windows\\System32\\DeviceSetupManagerAPI.dll", "action_type": "Metadata Refresh, \\Microsoft\\Windows\\Device Setup\\Metadata Refresh", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:43:46", "extension": "Task Run", "full_path": "usoclient.exe", "action_time": "C:\\Windows\\system32\\usoclient.exe", "action_type": "Schedule Work, \\Microsoft\\Windows\\UpdateOrchestrator\\Schedule Work", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 14:36:40", "extension": "Task Run", "full_path": "rundll32.exe", "action_time": "C:\\Windows\\system32\\rundll32.exe", "action_type": "CleanupTemporaryState, \\Microsoft\\Windows\\ApplicationData\\CleanupTemporaryState", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 14:36:40", "extension": "Task Run", "full_path": "usoclient.exe", "action_time": "C:\\Windows\\system32\\usoclient.exe", "action_type": "Report policies, \\Microsoft\\Windows\\UpdateOrchestrator\\Report policies", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/23 2:36:28", "extension": "Task Run", "full_path": "sppcext.dll", "action_time": "C:\\Windows\\System32\\sppcext.dll", "action_type": "SvcRestartTask, \\Microsoft\\Windows\\SoftwareProtectionPlatform\\SvcRestartTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/24 14:40:39", "extension": "Task Run", "full_path": "tzsync.exe", "action_time": "C:\\Windows\\system32\\tzsync.exe", "action_type": "SynchronizeTimeZone, \\Microsoft\\Windows\\Time Zone\\SynchronizeTimeZone", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/24 14:40:39", "extension": "Task Run", "full_path": "", "action_time": "", "action_type": "StartComponentCleanup, \\Microsoft\\Windows\\Servicing\\StartComponentCleanup", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 1:55:10", "extension": "Task Run", "full_path": "AppListBackupLauncher.dll", "action_time": "C:\\Windows\\system32\\AppListBackupLauncher.dll", "action_type": "BackupNonMaintenance, \\Microsoft\\Windows\\AppListBackup\\BackupNonMaintenance", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 4:17:36", "extension": "Task Run", "full_path": "", "action_time": "", "action_type": "PerformRemediation, \\Microsoft\\Windows\\WaaSMedic\\PerformRemediation", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 19:30:04", "extension": "Task Run", "full_path": "lpremove.exe", "action_time": "C:\\Windows\\system32\\lpremove.exe", "action_type": "LPRemove, \\Microsoft\\Windows\\MUI\\LPRemove", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 19:31:20", "extension": "Task Run", "full_path": "", "action_time": "", "action_type": "BgTaskRegistrationMaintenanceTask, \\Microsoft\\Windows\\BrokerInfrastructure\\BgTaskRegistrationMaintenanceTask", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "rundll32.exe", "action_time": "C:\\Windows\\system32\\rundll32.exe", "action_type": "MaintenanceTasks, \\Microsoft\\Windows\\StateRepository\\MaintenanceTasks", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "AppHostRegistrationVerifier.exe", "action_time": "C:\\Windows\\system32\\AppHostRegistrationVerifier.exe", "action_type": "appuriverifierdaily, \\Microsoft\\Windows\\ApplicationData\\appuriverifierdaily", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "rundll32.exe", "action_time": "C:\\Windows\\system32\\rundll32.exe", "action_type": "maintenancetasks, \\Microsoft\\Windows\\capabilityaccessmanager\\maintenancetasks", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "AppListBackupLauncher.dll", "action_time": "C:\\Windows\\system32\\AppListBackupLauncher.dll", "action_type": "Backup, \\Microsoft\\Windows\\AppListBackup\\Backup", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "sc.exe", "action_time": "C:\\Windows\\system32\\sc.exe", "action_type": "SynchronizeTime, \\Microsoft\\Windows\\Time Synchronization\\SynchronizeTime", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "StorageUsage.dll", "action_time": "C:\\Windows\\system32\\StorageUsage.dll", "action_type": "StorageSense, \\Microsoft\\Windows\\DiskFootprint\\StorageSense", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "TempSignedLicenseExchangeTask.dll", "action_time": "C:\\Windows\\System32\\TempSignedLicenseExchangeTask.dll", "action_type": "TempSignedLicenseExchange, \\Microsoft\\Windows\\License Manager\\TempSignedLicenseExchange", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "dstokenclean.exe", "action_time": "C:\\Windows\\system32\\dstokenclean.exe", "action_type": "DsSvcCleanup, \\Microsoft\\Windows\\ApplicationData\\DsSvcCleanup", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:18", "extension": "Task Run", "full_path": "WofTasks.dll", "action_time": "C:\\Windows\\system32\\WofTasks.dll", "action_type": "WIM-Hash-Validation, \\Microsoft\\Windows\\WOF\\WIM-Hash-Validation", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:44:34", "extension": "Task Run", "full_path": "energytask.dll", "action_time": "C:\\Windows\\System32\\energytask.dll", "action_type": "AnalyzeSystem, \\Microsoft\\Windows\\Power Efficiency Diagnostics\\AnalyzeSystem", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:45:41", "extension": "Task Run", "full_path": "la57setup.exe", "action_time": "C:\\Windows\\system32\\la57setup.exe", "action_type": "La57Cleanup, \\Microsoft\\Windows\\Kernel\\La57Cleanup", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 18:45:41", "extension": "Task Run", "full_path": "rundll32.exe", "action_time": "C:\\Windows\\system32\\rundll32.exe", "action_type": "StartupAppTask, \\Microsoft\\Windows\\Application Experience\\StartupAppTask", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:49", "extension": "Task Run", "full_path": "MBMediaManager.dll", "action_time": "C:\\Windows\\System32\\MBMediaManager.dll", "action_type": "OobeDiscovery, \\Microsoft\\Windows\\WwanSvc\\OobeDiscovery", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:49", "extension": "Task Run", "full_path": "sdbinst.exe", "action_time": "C:\\Windows\\system32\\sdbinst.exe", "action_type": "SdbinstMergeDbTask, \\Microsoft\\Windows\\Application Experience\\SdbinstMergeDbTask", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:49", "extension": "Task Run", "full_path": "ClipRenew.exe", "action_time": "C:\\Windows\\system32\\ClipRenew.exe", "action_type": "EnableLicenseAcquisition, \\Microsoft\\Windows\\Subscription\\EnableLicenseAcquisition", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "MicrosoftEdgeUpdate.exe", "action_time": "C:\\Program Files (x86)\\Microsoft\\EdgeUpdate\\MicrosoftEdgeUpdate.exe", "action_type": "MicrosoftEdgeUpdateTaskMachineCore{25584E2E-9B5C-42B9-9FCF-149B17886BF2}, \\MicrosoftEdgeUpdateTaskMachineCore{25584E2E-9B5C-42B9-9FCF-149B17886BF2}", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "mscms.dll", "action_time": "C:\\Windows\\System32\\mscms.dll", "action_type": "Calibration Loader, \\Microsoft\\Windows\\WindowsColorSystem\\Calibration Loader", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "wininet.dll", "action_time": "C:\\Windows\\system32\\wininet.dll", "action_type": "CacheTask, \\Microsoft\\Windows\\Wininet\\CacheTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "sppcext.dll", "action_time": "C:\\Windows\\System32\\sppcext.dll", "action_type": "SvcRestartTaskLogon, \\Microsoft\\Windows\\SoftwareProtectionPlatform\\SvcRestartTaskLogon", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "PlaySndSrv.dll", "action_time": "C:\\Windows\\System32\\PlaySndSrv.dll", "action_type": "SystemSoundsService, \\Microsoft\\Windows\\Multimedia\\SystemSoundsService", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "pnpui.dll", "action_time": "C:\\Windows\\System32\\pnpui.dll", "action_type": "Device Install Reboot Required, \\Microsoft\\Windows\\Plug and Play\\Device Install Reboot Required", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "Task Run", "full_path": "MsCtfMonitor.dll", "action_time": "C:\\Windows\\system32\\MsCtfMonitor.dll", "action_type": "MsCtfMonitor, \\Microsoft\\Windows\\TextServicesFramework\\MsCtfMonitor", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:52", "extension": "Task Run", "full_path": "TimeSyncTask.dll", "action_time": "C:\\Windows\\system32\\TimeSyncTask.dll", "action_type": "ForceSynchronizeTime, \\Microsoft\\Windows\\Time Synchronization\\ForceSynchronizeTime", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:55", "extension": "Task Run", "full_path": "TpmTasks.dll", "action_time": "C:\\Windows\\system32\\TpmTasks.dll", "action_type": "Tpm-Maintenance, \\Microsoft\\Windows\\TPM\\Tpm-Maintenance", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:56", "extension": "Task Run", "full_path": "ngctasks.dll", "action_time": "C:\\Windows\\system32\\ngctasks.dll", "action_type": "KeyPreGenTask, \\Microsoft\\Windows\\CertificateServicesClient\\KeyPreGenTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:57", "extension": "Task Run", "full_path": "ngctasks.dll", "action_time": "C:\\Windows\\system32\\ngctasks.dll", "action_type": "AikCertEnrollTask, \\Microsoft\\Windows\\CertificateServicesClient\\AikCertEnrollTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:57", "extension": "Task Run", "full_path": "sppcext.dll", "action_time": "C:\\Windows\\System32\\sppcext.dll", "action_type": "SvcRestartTaskNetwork, \\Microsoft\\Windows\\SoftwareProtectionPlatform\\SvcRestartTaskNetwork", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:36:21", "extension": "Task Run", "full_path": "CoreGlobConfig.dll", "action_time": "C:\\Windows\\System32\\CoreGlobConfig.dll", "action_type": "Synchronize Language Settings, \\Microsoft\\Windows\\International\\Synchronize Language Settings", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:36:23", "extension": "Task Run", "full_path": "shell32.dll", "action_time": "C:\\Windows\\system32\\shell32.dll", "action_type": "CreateObjectTask, \\Microsoft\\Windows\\Shell\\CreateObjectTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:36:49", "extension": "Task Run", "full_path": "dxgiadaptercache.exe", "action_time": "C:\\Windows\\system32\\dxgiadaptercache.exe", "action_type": "DXGIAdapterCache, \\Microsoft\\Windows\\DirectX\\DXGIAdapterCache", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:36:51", "extension": "Task Run", "full_path": "WiFiCloudStore.dll", "action_time": "C:\\Windows\\System32\\WiFiCloudStore.dll", "action_type": "CDSSync, \\Microsoft\\Windows\\WlanSvc\\CDSSync", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:40:53", "extension": "Task Run", "full_path": "sc.exe", "action_time": "C:\\Windows\\system32\\sc.exe", "action_type": "LoginCheck, \\Microsoft\\Windows\\PushToInstall\\LoginCheck", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:43:11", "extension": "Task Run", "full_path": "MusNotification.exe", "action_time": "C:\\Windows\\system32\\MusNotification.exe", "action_type": "USO_UxBroker, \\Microsoft\\Windows\\UpdateOrchestrator\\USO_UxBroker", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:43:11", "extension": "Task Run", "full_path": "sc.exe", "action_time": "C:\\Windows\\system32\\sc.exe", "action_type": "Registration, \\Microsoft\\Windows\\PushToInstall\\Registration", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:43:12", "extension": "Task Run", "full_path": "UIEOrchestrator.exe", "action_time": "C:\\Windows\\system32\\UIEOrchestrator.exe", "action_type": "UIEOrchestrator, \\Microsoft\\Windows\\UpdateOrchestrator\\UIEOrchestrator", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:49:54", "extension": "Task Run", "full_path": "ProvTool.exe", "action_time": "C:\\Windows\\system32\\ProvTool.exe", "action_type": "Logon, \\Microsoft\\Windows\\Management\\Provisioning\\Logon", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:49:54", "extension": "Task Run", "full_path": "UCPDMgr.exe", "action_time": "C:\\Windows\\system32\\UCPDMgr.exe", "action_type": "UCPD velocity, \\Microsoft\\Windows\\AppxDeploymentClient\\UCPD velocity", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:50:53", "extension": "Task Run", "full_path": "LanguageComponentsInstaller.dll", "action_time": "C:\\Windows\\System32\\LanguageComponentsInstaller.dll", "action_type": "Installation, \\Microsoft\\Windows\\LanguageComponentsInstaller\\Installation", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 5:08:06", "extension": "Task Run", "full_path": "cleanmgr.exe", "action_time": "C:\\Windows\\system32\\cleanmgr.exe", "action_type": "SilentCleanup, \\Microsoft\\Windows\\DiskCleanup\\SilentCleanup", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 5:35:53", "extension": "Task Run", "full_path": "InstallServiceTasks.dll", "action_time": "C:\\Windows\\System32\\InstallServiceTasks.dll", "action_type": "RestoreDevice, \\Microsoft\\Windows\\InstallService\\RestoreDevice", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 6:28:48", "extension": "Task Run", "full_path": "DeviceDirectoryClient.dll", "action_time": "C:\\Windows\\system32\\DeviceDirectoryClient.dll", "action_type": "RegisterDeviceSettingChange, \\Microsoft\\Windows\\DeviceDirectoryClient\\RegisterDeviceSettingChange", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 6:35:53", "extension": "Task Run", "full_path": "", "action_time": "", "action_type": "ReconcileLanguageResources, \\Microsoft\\Windows\\LanguageComponentsInstaller\\ReconcileLanguageResources", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 6:40:53", "extension": "Task Run", "full_path": "usoclient.exe", "action_time": "C:\\Windows\\system32\\usoclient.exe", "action_type": "Schedule Scan Static Task, \\Microsoft\\Windows\\UpdateOrchestrator\\Schedule Scan Static Task", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 7:44:23", "extension": "Task Run", "full_path": "usoclient.exe", "action_time": "C:\\Windows\\system32\\usoclient.exe", "action_type": "Schedule Scan, \\Microsoft\\Windows\\UpdateOrchestrator\\Schedule Scan", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 12:35:53", "extension": "Task Run", "full_path": "dimsjob.dll", "action_time": "C:\\Windows\\system32\\dimsjob.dll", "action_type": "UserTask, \\Microsoft\\Windows\\CertificateServicesClient\\UserTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 12:36:03", "extension": "Task Run", "full_path": "dimsjob.dll", "action_time": "C:\\Windows\\system32\\dimsjob.dll", "action_type": "SystemTask, \\Microsoft\\Windows\\CertificateServicesClient\\SystemTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 13:50:04", "extension": "Task Run", "full_path": "unifiedconsent.dll", "action_time": "C:\\Windows\\System32\\unifiedconsent.dll", "action_type": "UnifiedConsentSyncTask, \\Microsoft\\Windows\\ConsentUX\\UnifiedConsent\\UnifiedConsentSyncTask", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 15:54:59", "extension": "Task Run", "full_path": "wosc.dll", "action_time": "C:\\Windows\\System32\\wosc.dll", "action_type": "RefreshCache, \\Microsoft\\Windows\\Flighting\\OneSettings\\RefreshCache", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 16:05:07", "extension": "Task Run", "full_path": "douyin_guard.exe", "action_time": "D:\\¶¶Òô\\douyin\\douyin_guard.exe", "action_type": "LaunchDouyinGuard, \\<PERSON>uyinUser\\<PERSON>uyinGuard\\LaunchDouyinGuard", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 16:39:16", "extension": "Task Run", "full_path": "updater.exe\"", "action_time": "\"C:\\Users\\<USER>\\AppData\\Local\\QuarkUpdater\\QuarkUpdater\\1.0.0.12\\updater.exe\"", "action_type": "QuarkUpdaterTaskUser1.0.0.12{FC8D82EB-DBC0-439C-9BB0-74E2D15DE587}, \\QuarkUpdaterUser\\QuarkUpdater\\QuarkUpdaterTaskUser1.0.0.12{FC8D82EB-DBC0-439C-9BB0-74E2D15DE587}", "process_name": "exe\"", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 16:40:49", "extension": "Task Run", "full_path": "TpmTasks.dll", "action_time": "C:\\Windows\\system32\\TpmTasks.dll", "action_type": "Secure-Boot-Update, \\Microsoft\\Windows\\PI\\Secure-Boot-Update", "process_name": "dll", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 16:45:16", "extension": "Task Run", "full_path": "MicrosoftEdgeUpdate.exe", "action_time": "C:\\Program Files (x86)\\Microsoft\\EdgeUpdate\\MicrosoftEdgeUpdate.exe", "action_type": "MicrosoftEdgeUpdateTaskMachineUA{EF574032-C574-4B52-AFC3-EF62C7BC5D01}, \\MicrosoftEdgeUpdateTaskMachineUA{EF574032-C574-4B52-AFC3-EF62C7BC5D01}", "process_name": "exe", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:13", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "DESKTOP-UN4PVRC\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:04", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "DESKTOP-UN4PVRC\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:57:42", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "DESKTOP-UN4PVRC\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:33", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "54EBCE90C07255E\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:24:56", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "3CAE14F1009CD9C\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:29:35", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "B0FFA5B1BDFC261\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:00", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "68F5CCDC7F61CD9\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:25", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "7F85CFFFFB0FF18\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:49:40", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "B830C64FFB35F78\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:33", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "EADAAC5E5AAEFBA\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:12", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "0D08FCCBB6EC4D4\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:13", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "4CB3B3EA92ABDDE\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:20", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "42DAD48C8F93CE6\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:28", "extension": "User <PERSON>", "full_path": "", "action_time": "", "action_type": "33CED5EA2E7DB8A\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:18", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:18", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:18", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:23", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:25", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:25", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:25", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:25", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:22", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:22", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:22", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:20:23", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:48:59", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:58:05", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:58:06", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:58:06", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:58:07", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:01:07", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:52", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:52", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:52", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 0:28:53", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:25:31", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:25:31", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:25:31", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:25:38", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:29:59", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:29:59", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:29:59", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:30:00", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 6:38:36", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 6:38:36", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 6:38:37", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 6:38:38", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 18:57:18", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 18:57:18", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 18:57:19", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 18:57:20", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 21:21:55", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 21:21:55", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 21:21:56", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 21:21:57", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/14 19:55:20", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/14 19:55:20", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/14 19:55:20", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/14 19:55:21", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:32", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:32", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:33", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:54:34", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:44", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:44", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:45", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 1:28:45", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:50:01", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:50:01", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:50:01", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/20 20:50:03", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:56", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:56", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:56", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/21 16:22:58", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:39", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:39", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:39", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/22 2:36:40", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:32", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:32", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:33", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 2:27:34", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 11:59:05", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 11:59:05", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 11:59:06", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/26 11:59:07", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 11:07:13", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 11:07:13", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 11:07:13", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 11:07:14", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:39", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:39", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:39", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 3:16:41", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 6:01:06", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 6:01:06", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 6:01:07", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/28 6:01:08", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:48", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-0", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:48", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\UMFD-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:49", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\DWM-1", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 4:35:50", "extension": "User Logon", "full_path": "", "action_time": "", "action_type": "WORKGROUP\\XOS", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 21:59:49", "extension": "View Folder in Explorer", "full_path": "SystemResources", "action_time": "C:\\Windows\\SystemResources", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 21:59:50", "extension": "View Folder in Explorer", "full_path": "pris", "action_time": "C:\\Windows\\SystemResources\\ShellComponents\\pris", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\0\\0\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 21:59:50", "extension": "View Folder in Explorer", "full_path": "ShellComponents", "action_time": "C:\\Windows\\SystemResources\\ShellComponents", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\0\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 21:59:56", "extension": "View Folder in Explorer", "full_path": "SoftwareDistribution", "action_time": "C:\\Windows\\SoftwareDistribution", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 21:59:58", "extension": "View Folder in Explorer", "full_path": "5b60837cfe0b5db1e04da2fc075f2504", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\5b60837cfe0b5db1e04da2fc075f2504", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:00", "extension": "View Folder in Explorer", "full_path": "0e849b0acfa6022a84cc16cf594b96fc", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\0e849b0acfa6022a84cc16cf594b96fc", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:02", "extension": "View Folder in Explorer", "full_path": "2c33a38db038bb37df1340a9ebf7a585", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\2c33a38db038bb37df1340a9ebf7a585", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\2", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:04", "extension": "View Folder in Explorer", "full_path": "b2014b1c9b523bb059d0cc1f3a7323ec", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\b2014b1c9b523bb059d0cc1f3a7323ec", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\3", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:06", "extension": "View Folder in Explorer", "full_path": "b8daa7a0548578094c580748df8796f8", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\b8daa7a0548578094c580748df8796f8", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\4", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:09", "extension": "View Folder in Explorer", "full_path": "fdce38ca2d834e0825703a292ed60991", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\fdce38ca2d834e0825703a292ed60991", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\5", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:11", "extension": "View Folder in Explorer", "full_path": "fb9125ed4074119adea1ff7f62be78de", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\fb9125ed4074119adea1ff7f62be78de", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\6", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:13", "extension": "View Folder in Explorer", "full_path": "f7157a93f1973251ff07b7fa56088079", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\f7157a93f1973251ff07b7fa56088079", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\7", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:15", "extension": "View Folder in Explorer", "full_path": "ee0547d6c0c98c745e201db8f28ef363", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\ee0547d6c0c98c745e201db8f28ef363", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\8", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:17", "extension": "View Folder in Explorer", "full_path": "e06aeae46e96c9312e04d752761f53bd", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\e06aeae46e96c9312e04d752761f53bd", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\9", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:19", "extension": "View Folder in Explorer", "full_path": "c88a00dc40451f77745524b7ab7abfe5", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\c88a00dc40451f77745524b7ab7abfe5", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\10", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:25", "extension": "View Folder in Explorer", "full_path": "b02e189b25f8e4c1bdc032681b58326a", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\b02e189b25f8e4c1bdc032681b58326a", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\11", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:28", "extension": "View Folder in Explorer", "full_path": "aa1e73f47f2fd04b51fc5324f663033c", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\aa1e73f47f2fd04b51fc5324f663033c", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\12", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:30", "extension": "View Folder in Explorer", "full_path": "a28ce8c5d58096a07b034d4b79b12a4a", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\a28ce8c5d58096a07b034d4b79b12a4a", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\13", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:32", "extension": "View Folder in Explorer", "full_path": "Download", "action_time": "C:\\Windows\\SoftwareDistribution\\Download", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:53", "extension": "View Folder in Explorer", "full_path": "Package_for_RollupFix~~amd64~~22621.5262.1.14", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\603342b720175919acf9d4db19ba001c\\Package_for_RollupFix~~amd64~~22621.5262.1.14", "action_type": "", "process_name": "14", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\14\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:00:55", "extension": "View Folder in Explorer", "full_path": "603342b720175919acf9d4db19ba001c", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\603342b720175919acf9d4db19ba001c", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\14", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:01:01", "extension": "View Folder in Explorer", "full_path": "<PERSON><PERSON><PERSON>", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\603342b720175919acf9d4db19ba001c\\Metadata", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\14\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:01:01", "extension": "View Folder in Explorer", "full_path": "Windows11.0-KB5055629-x64", "action_time": "C:\\Windows\\SoftwareDistribution\\Download\\603342b720175919acf9d4db19ba001c\\Metadata\\Windows11.0-KB5055629-x64", "action_type": "", "process_name": "0-KB5055629-x64", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\1\\0\\14\\1\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:25:50", "extension": "View Folder in Explorer", "full_path": "amd", "action_time": "amd", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1\\2\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/9 22:26:15", "extension": "View Folder in Explorer", "full_path": "nvidia", "action_time": "nvidia", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1\\2\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/10 12:57:20", "extension": "View Folder in Explorer", "full_path": "service-backups", "action_time": "C:\\Windows\\service-backups", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\2", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/10 12:58:41", "extension": "View Folder in Explorer", "full_path": "dep-valorant-faceit", "action_time": "dep-valorant-faceit", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1\\6\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/10 12:58:43", "extension": "View Folder in Explorer", "full_path": "dep-valorant-faceit", "action_time": "dep-valorant-faceit", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1\\6\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/10 12:58:45", "extension": "View Folder in Explorer", "full_path": "UAC", "action_time": "UAC", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1\\6\\2", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/5/10 12:58:51", "extension": "View Folder in Explorer", "full_path": "UAC", "action_time": "UAC", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1\\6\\3", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:08:49", "extension": "View Folder in Explorer", "full_path": "DrvCeonw", "action_time": "D:\\DrvCeonw", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:10:41", "extension": "View Folder in Explorer", "full_path": "XOS", "action_time": "C:\\XOS", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:56", "extension": "View Folder in Explorer", "full_path": "DirectX_Repair(Enhanced_Edition)", "action_time": "DirectX_Repair(Enhanced_Edition)", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2\\1\\2\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:55:26", "extension": "View Folder in Explorer", "full_path": "11111", "action_time": "D:\\11111", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2\\1", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:56:35", "extension": "View Folder in Explorer", "full_path": "SysWOW64", "action_time": "C:\\Windows\\SysWOW64", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0\\3", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:56:35", "extension": "View Folder in Explorer", "full_path": "Windows", "action_time": "C:\\Windows", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 1:32:10", "extension": "View Folder in Explorer", "full_path": "11111", "action_time": "D:\\11111", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2\\3", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/15 2:46:09", "extension": "View Folder in Explorer", "full_path": "11111", "action_time": "D:\\11111", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2\\5", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:41:27", "extension": "View Folder in Explorer", "full_path": "11111", "action_time": "D:\\11111", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2\\7", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 7:59:24", "extension": "View Folder in Explorer", "full_path": "", "action_time": "C:\\", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\0", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/29 16:54:57", "extension": "View Folder in Explorer", "full_path": "", "action_time": "D:\\", "action_type": "", "process_name": "", "process_id": "HKEY_CURRENT_USER\\Software\\Classes\\Local Settings\\Software\\Microsoft\\Windows\\Shell\\BagMRU\\\\1\\2", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:24", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:28", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:30", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:31", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:31", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:34", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:37", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:38", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:39", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:39", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:42", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:42", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:44", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:44", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:44", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:02", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:05", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:08", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:12", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:15", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:15", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:18", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:18", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:20", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:20", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:23", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:23", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:03", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:03", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:04", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:04", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:05", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:05", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:40", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:43", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:44", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:53", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:16", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:17", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:20", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:21", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:23", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:24", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:27", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:28", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:36", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:37", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 8:00:16", "extension": "Windows Installer Ended", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:24", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:26", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:29", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:30", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:31", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:33", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:36", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:38", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:38", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:39", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:39", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:43", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:07:43", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:40", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:41", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:43", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:44", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:46:44", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:01", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:05", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:08", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:11", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:15", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:15", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:18", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:18", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:20", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:20", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:23", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:47:23", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:51:59", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:00", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:01", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:02", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:03", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:03", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:03", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:04", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:05", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/12 23:52:05", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:39", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:42", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:43", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 2:39:48", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:16", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:16", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:20", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:20", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:23", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/13 3:10:23", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:26", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:27", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:30", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/17 4:37:36", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}, {"filename": "2025/7/27 7:59:49", "extension": "Windows Installer Started", "full_path": "", "action_time": "", "action_type": "", "process_name": "", "process_id": "", "user_name": "", "file_size": "", "file_attributes": "", "file_created_time": "", "file_modified_time": "", "file_accessed_time": "", "more_information": ""}]